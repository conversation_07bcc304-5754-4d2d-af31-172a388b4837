# 📱 دليل تشغيل تطبيق الصيدلية على الهاتف

## 🚀 خطوات التشغيل السريع:

### 1. **تشغيل الخادم على الكمبيوتر:**
```bash
cd backend
node server.js
```
**يجب أن ترى:**
```
🚀 Server running on port 3000 in production mode
📱 Mobile access: http://192.168.1.12:3000
✅ Connected to MongoDB successfully
```

### 2. **بناء APK:**
- شغل ملف `build_apk.bat`
- أو استخدم الأمر: `flutter build apk --release`

### 3. **تنزيل APK على الهاتف:**
- انسخ ملف `build\app\outputs\flutter-apk\app-release.apk`
- انقله للهاتف وثبته

### 4. **تأكد من الشبكة:**
- الهاتف والكمبيوتر على نفس شبكة Wi-Fi
- اختبر الاتصال من التطبيق بالضغط على 🌐

---

## 🔧 إعدادات مهمة:

### **تغيير IP الخادم:**
إذا كان IP الكمبيوتر مختلف:

1. **اعرف IP الكمبيوتر:**
   ```bash
   ipconfig
   ```
   ابحث عن `IPv4 Address` في `Wireless LAN adapter Wi-Fi`

2. **حديث الكود:**
   في `lib/config/app_config.dart`:
   ```dart
   static const String _localIP = 'IP_الجديد_هنا';
   ```

3. **أعد بناء APK:**
   ```bash
   flutter build apk --release
   ```

### **حل مشاكل الاتصال:**

#### **"لا يمكن الاتصال بالخادم":**
- ✅ تأكد من تشغيل الخادم
- ✅ تحقق من IP الصحيح
- ✅ تأكد من نفس الشبكة
- ✅ أغلق الجدار الناري مؤقتاً

#### **"انتهت مهلة الاتصال":**
- ✅ تحقق من سرعة الإنترنت
- ✅ أعد تشغيل الخادم
- ✅ أعد تشغيل الراوتر

---

## 🛠️ أدوات التشخيص في التطبيق:

### **زر اختبار الشبكة 🌐:**
- موجود في صفحات تسجيل الدخول والتسجيل
- يختبر الاتصال بالخادم
- يعرض رسائل مفصلة عن المشاكل

### **معلومات التشخيص:**
- تظهر في console عند تشغيل التطبيق
- تحتوي على IP والمنفذ المستخدم

---

## 📋 قائمة التحقق قبل التشغيل:

- [ ] الخادم يعمل على الكمبيوتر
- [ ] MongoDB متصل
- [ ] الهاتف والكمبيوتر على نفس الشبكة
- [ ] IP صحيح في الكود
- [ ] APK محديث
- [ ] أذونات التطبيق مفعلة

---

## 🎯 نصائح للأداء الأفضل:

1. **استخدم شبكة Wi-Fi سريعة**
2. **أغلق التطبيقات الأخرى على الهاتف**
3. **تأكد من شحن الهاتف**
4. **أعد تشغيل الخادم كل فترة**

---

## 📞 في حالة المشاكل:

1. **اختبر الاتصال** بزر 🌐
2. **تحقق من logs الخادم**
3. **أعد بناء APK** إذا غيرت الإعدادات
4. **تأكد من إعدادات الشبكة**
