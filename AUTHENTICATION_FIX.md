# حل مشكلة المصادقة في تتبع الطلبات

## 🔍 المشكلة المكتشفة

من خلال سجلات الخادم، لاحظنا الخطأ التالي:
```
127.0.0.1 - - [28/Jul/2025:17:47:33 +0000] "GET /api/orders/ORD-1753724781316-0038 HTTP/1.1" 401 100
```

هذا يعني أن الطلب للحصول على بيانات الطلب فشل بسبب عدم وجود مصادقة صحيحة (401 Unauthorized).

## 🛠️ الحلول المطبقة

### 1. **تحسين معالجة الأخطاء في `OrderService.getOrderById()`**

```dart
/// الحصول على طلب محدد بـ ID (للعميل)
static Future<Map<String, dynamic>> getOrderById({
  required String orderId,
}) async {
  try {
    // التحقق من وجود التوكن
    final token = await UserService.getAccessToken();
    if (token == null) {
      return {
        'success': false,
        'message': 'يجب تسجيل الدخول أولاً',
        'needLogin': true,
      };
    }

    final response = await http.get(
      Uri.parse('${AppConfig.baseUrl}/orders/$orderId'),
      headers: await userAuthHeaders,
    );

    if (response.statusCode == 401) {
      return {
        'success': false,
        'message': 'انتهت صلاحية جلسة المستخدم، يرجى تسجيل الدخول مرة أخرى',
        'needLogin': true,
      };
    }
    // ... باقي الكود
  }
}
```

### 2. **إضافة حوار تسجيل الدخول في `OrderTrackingScreen`**

```dart
/// إظهار حوار يطلب تسجيل الدخول
void _showLoginRequiredDialog() {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Row(
          children: [
            Icon(Icons.login, color: Colors.orange, size: 28),
            const SizedBox(width: 12),
            Text('تسجيل الدخول مطلوب'),
          ],
        ),
        content: Text(
          'انتهت صلاحية جلسة المستخدم. يرجى تسجيل الدخول مرة أخرى لمتابعة تتبع الطلب.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // إغلاق الحوار
              Navigator.pop(context); // العودة للصفحة السابقة
            },
            child: Text('إلغاء'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
              // يمكن إضافة الانتقال لصفحة تسجيل الدخول هنا
            },
            icon: const Icon(Icons.login, size: 18),
            label: Text('تسجيل الدخول'),
          ),
        ],
      );
    },
  );
}
```

### 3. **معالجة الأخطاء في `_fetchRealTimeOrderData()`**

```dart
} else {
  setState(() {
    _errorMessage = result['message'] ?? 'فشل في جلب بيانات الطلب';
    _isLoading = false;
  });

  // إذا كانت المشكلة في المصادقة، أظهر رسالة خاصة
  if (result['needLogin'] == true) {
    _showLoginRequiredDialog();
  }
}
```

### 4. **إضافة نفس المعالجة في `OffersScreen`**

تم إضافة نفس منطق معالجة أخطاء المصادقة في `offers_screen.dart` عند قبول العروض.

## 🎯 النتائج المتوقعة

### ✅ **قبل التحديث:**
- خطأ 401 بدون رسالة واضحة للمستخدم
- التطبيق يحاول التحديث كل 10 ثواني ويفشل
- المستخدم لا يعرف سبب المشكلة

### ✅ **بعد التحديث:**
- رسالة واضحة للمستخدم عن انتهاء صلاحية الجلسة
- حوار يطلب تسجيل الدخول مرة أخرى
- إيقاف المحاولات المتكررة عند فشل المصادقة
- تجربة مستخدم أفضل وأكثر وضوحاً

## 🔧 خطوات إضافية مقترحة

### 1. **إضافة تجديد تلقائي للتوكن**
```dart
// في UserService
static Future<bool> refreshTokenIfNeeded() async {
  // منطق تجديد التوكن تلقائياً
}
```

### 2. **إضافة الانتقال المباشر لصفحة تسجيل الدخول**
```dart
// في _showLoginRequiredDialog()
Navigator.pushNamedAndRemoveUntil(
  context, 
  '/login', 
  (route) => false
);
```

### 3. **حفظ حالة الطلب محلياً**
```dart
// حفظ ID الطلب لاستكمال التتبع بعد تسجيل الدخول
SharedPreferences.setString('pending_order_id', orderId);
```

## 📊 سيناريوهات الاستخدام

| الحالة | السلوك القديم | السلوك الجديد |
|--------|---------------|---------------|
| توكن منتهي الصلاحية | خطأ صامت | حوار تسجيل دخول |
| لا يوجد توكن | محاولات متكررة | رسالة واضحة |
| خطأ شبكة | رسالة عامة | رسالة محددة |
| طلب غير موجود | خطأ 404 | رسالة مفهومة |

هذه التحديثات تحسن تجربة المستخدم بشكل كبير وتوفر معالجة احترافية لأخطاء المصادقة! 🚀
