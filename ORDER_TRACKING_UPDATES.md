# تحديثات شاشة تتبع الطلبات للعميل - Customer Order Tracking Screen Updates

## التحديثات المضافة

### 1. ربط البيانات الحقيقية من الخادم (للعميل)
- تم إضافة استيراد `OrderService` للتواصل مع الخادم
- تم إضافة وظيفة `getOrderById()` في `OrderService` لجلب طلب محدد بـ ID
- تم تحديث `_fetchRealTimeOrderData()` لاستخدام `getOrderById()` بدلاً من البحث في قائمة الطلبات
- تم إضافة مؤقت `_dataRefreshTimer` لتحديث البيانات كل 10 ثواني تلقائياً (تحديث سريع)

### 2. عرض البيانات الحقيقية
- **معلومات السائق**: تم تحديث `_getDriverName()` و `_getDriverPhone()` لاستخراج البيانات من `delivery.driver`
- **قائمة الأدوية**: تم إضافة `_buildMedicinesList()` لعرض الأدوية من `items` أو `medicines` أو `medicineRequest`
- **المبلغ الإجمالي**: تم إضافة `_getTotalAmount()` لحساب المبلغ من `pricing.total` أو العروض المقبولة
- **العملة**: تم إضافة `_getCurrency()` لعرض العملة الصحيحة (جنيه/ريال/دولار)

### 3. معلومات إضافية عن الطلب
- **رقم الطلب**: عرض `orderNumber` أو `id`
- **تاريخ الطلب**: عرض `createdAt` بتنسيق مقروء
- **حالة الدفع**: عرض `paymentStatus` مع الألوان المناسبة

### 4. تأكيد استلام الطلب (للعميل)
- تم تحديث `_confirmDelivery()` لاستخدام `confirmOrder()` بدلاً من `updateOrderStatus()`
- إضافة مؤشر تحميل أثناء تأكيد الاستلام
- عرض رسائل نجاح أو خطأ حسب استجابة الخادم

### 5. إشعارات تغيير الحالة الفورية
- **إشعار فوري**: وظيفة `_showStatusChangeNotification()` لإظهار إشعار عند تغيير حالة الطلب
- **تحديث مباشر**: عندما تضغط الصيدلية على "بدء التجهيز" يظهر للعميل فوراً "جاري التجهيز"
- **ألوان مميزة**: كل حالة لها لون وأيقونة مختلفة في الإشعار

### 6. التواصل مع السائق
- **الاتصال بالسائق**: وظيفة `_callDriver()` لفتح تطبيق الهاتف والاتصال بالسائق
- **إرسال رسائل**: وظيفة `_messageDriver()` مع رسائل سريعة ورسائل مخصصة
- **رسائل سريعة**: "أين موقعك الحالي؟"، "كم الوقت المتبقي؟"، "هل يمكنك التواصل؟"

### 7. واجهة المستخدم المحسنة
- **مؤشر التحميل**: عرض مؤشر تحميل أثناء جلب البيانات
- **رسائل الخطأ**: عرض رسائل خطأ مع زر إعادة المحاولة
- **زر التحديث**: تفعيل زر التحديث في شريط التطبيق لجلب البيانات يدوياً
- **أزرار التواصل**: أزرار للاتصال والمراسلة مع السائق عند توفر معلوماته

## البيانات المدعومة

### من نموذج الطلب (Order Model)
```javascript
{
  "orderNumber": "ORD-123",
  "status": "out_for_delivery",
  "currency": "EGP",
  "paymentStatus": "paid",
  "createdAt": "2024-01-15T10:30:00Z",
  
  // معلومات التوصيل
  "delivery": {
    "driver": {
      "name": "أحمد محمد",
      "phone": "+20 ************",
      "vehicleInfo": "دراجة نارية"
    },
    "estimatedTime": "2024-01-15T11:00:00Z"
  },
  
  // الأدوية (للطلبات العادية)
  "items": [
    {
      "medicine": { "name": "باراسيتامول" },
      "quantity": 2,
      "price": 25.50
    }
  ],
  
  // أو للطلبات البسيطة
  "medicineRequest": {
    "name": "أسبرين",
    "quantity": 1
  },
  
  // التسعير
  "pricing": {
    "total": 155.75
  },
  
  // العروض (للطلبات التي تحتوي على عروض)
  "offers": [
    {
      "status": "accepted",
      "price": 120.00
    }
  ]
}
```

## كيفية الاستخدام

### 1. تمرير البيانات للشاشة
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => OrderTrackingScreen(
      orderId: "ORD-123",           // مطلوب لجلب البيانات من الخادم
      order: orderData,             // البيانات الأولية (اختياري)
      isDark: isDarkMode,
      onToggleDarkMode: toggleDarkMode,
      pharmacyAddress: "عنوان الصيدلية",
      userAddress: "عنوان المستخدم",
    ),
  ),
);
```

### 2. التحديث التلقائي
- البيانات تُحدث تلقائياً كل 30 ثانية
- يمكن التحديث يدوياً بالضغط على زر التحديث
- في حالة الخطأ، يظهر زر إعادة المحاولة

### 3. حالات الطلب المدعومة
- `confirmed`: تم تأكيد الطلب
- `preparing`: قيد التجهيز  
- `driver_assigned`: تم تعيين السائق
- `ready`: جاهز للتسليم
- `out_for_delivery`: جاري التوصيل
- `delivered`: تم الاستلام

## الملاحظات التقنية

1. **التوافق مع البيانات القديمة**: الكود يدعم البيانات المحلية والبيانات من الخادم
2. **معالجة الأخطاء**: تم إضافة معالجة شاملة للأخطاء مع رسائل واضحة
3. **الأداء**: استخدام Timer للتحديث التلقائي مع إلغاء المؤقتات عند إغلاق الشاشة
4. **تجربة المستخدم**: مؤشرات تحميل ورسائل واضحة للمستخدم

## المتطلبات

- `OrderService` يجب أن يكون متاحاً ويحتوي على:
  - `getOrderById()`: لجلب طلب محدد بـ ID للعميل
  - `confirmOrder()`: لتأكيد استلام الطلب من العميل
- الخادم يجب أن يدعم:
  - نموذج البيانات المذكور أعلاه
  - endpoint `GET /api/orders/:id` لجلب طلب محدد
  - تحديث فوري للحالة عند تغييرها من الصيدلية
- للتواصل مع السائق، يمكن إضافة مكتبات مثل `url_launcher` للاتصال والرسائل

## كيفية عمل التحديث الفوري

1. **الصيدلية تحدث الحالة**: عندما تضغط الصيدلية على "بدء التجهيز"
2. **الخادم يحدث البيانات**: يتم تحديث حالة الطلب في قاعدة البيانات
3. **العميل يحصل على التحديث**: خلال 10 ثواني كحد أقصى يحصل العميل على التحديث
4. **إشعار فوري**: يظهر للعميل إشعار بالحالة الجديدة مع لون وأيقونة مميزة
