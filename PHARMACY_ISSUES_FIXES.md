# إصلاح مشاكل الصيدلية - Pharmacy Issues Fixes

## المشاكل التي تم إصلاحها:

### 1. ✅ مشكلة enum في الخادم
**المشكلة:** `Customer` is not a valid enum value for path `updatedByModel`

**الحل:** تم إضافة `Customer` إلى قائمة القيم المسموحة في `backend/models/Order.js`:
```javascript
updatedByModel: {
  type: String,
  enum: ['User', 'Customer', 'Pharmacy', 'Admin'] // تم إضافة 'Customer'
}
```

### 2. ✅ مشكلة المبلغ الإجمالي يصبح صفر
**المشكلة:** عند تغيير حالة الطلب إلى "قيد التجهيز"، المبلغ الإجمالي يرجع لصفر

**الحل:** تم تحسين دالة `_getTotalAmount()` في `order_tracking_screen.dart`:
- إضافة البحث في `total` مباشرة
- إضافة البحث في `acceptedOffer.price`
- تحسين البحث في `items` مع `unitPrice` و `totalPrice`
- إضافة البحث في العروض للطلبات البسيطة
- إضافة فحوصات للتأكد من أن القيمة أكبر من صفر

### 3. ✅ مشكلة عدم ظهور بيانات السائق
**المشكلة:** بيانات السائق لا تظهر للعميل

**الحل:** تم تحسين دوال البحث عن بيانات السائق:

#### `_getDriverName()`:
- البحث في `delivery.driver.name`
- البحث في `driver.name` مباشرة
- البحث في `driverName` (البيانات المحلية)
- البحث في `assignedDriver.name`

#### `_getDriverPhone()`:
- البحث في `delivery.driver.phone`
- البحث في `driver.phone` مباشرة
- البحث في `driverPhone` (البيانات المحلية)
- البحث في `assignedDriver.phone`

### 4. ✅ تأكيد استلام الطلب
**الحل:** الدالة `confirmOrder()` موجودة في `OrderService` وتعمل بشكل صحيح.

## هيكل البيانات المتوقع من الخادم:

```json
{
  "id": "order_id",
  "orderNumber": "ORD-123456",
  "status": "out_for_delivery",
  "total": 150.0,
  "pricing": {
    "total": 150.0,
    "subtotal": 130.0,
    "deliveryFee": 20.0
  },
  "acceptedOffer": {
    "price": 150.0
  },
  "driver": {
    "name": "أحمد محمد",
    "phone": "+201234567890"
  },
  "delivery": {
    "driver": {
      "name": "أحمد محمد",
      "phone": "+201234567890",
      "vehicleInfo": "دراجة نارية"
    }
  },
  "assignedDriver": {
    "name": "أحمد محمد",
    "phone": "+201234567890"
  }
}
```

## التوصيات للخادم:

### 1. تحديث حالة الطلب
عند تحديث حالة الطلب إلى "قيد التجهيز"، تأكد من:
- الحفاظ على `total` أو `pricing.total`
- عدم حذف `acceptedOffer` إذا كان موجوداً
- الحفاظ على بيانات السائق في `driver` أو `delivery.driver`

### 2. تعيين السائق
عند تعيين سائق للطلب، تأكد من إضافة:
```json
{
  "driver": {
    "name": "اسم السائق",
    "phone": "رقم الهاتف"
  }
}
```

### 3. endpoint تأكيد الاستلام
تأكد من أن `/orders/:id/confirm` يدعم:
- `action: 'delivered'` لتأكيد الاستلام
- تحديث `updatedByModel` إلى `'Customer'`

## الاختبار:

### 1. اختبار المبلغ الإجمالي:
```bash
# تحديث حالة الطلب
PUT /api/orders/ORDER_ID/status
{
  "status": "preparing"
}

# التحقق من أن total لا يزال موجوداً
GET /api/orders/ORDER_ID
```

### 2. اختبار بيانات السائق:
```bash
# تعيين سائق
PUT /api/orders/ORDER_ID/assign-driver
{
  "driverId": "DRIVER_ID",
  "driverName": "أحمد محمد",
  "driverPhone": "+201234567890"
}
```

### 3. اختبار تأكيد الاستلام:
```bash
PUT /api/orders/ORDER_ID/confirm
{
  "action": "delivered"
}
```

## ملاحظات مهمة:

1. **تم إصلاح enum في الخادم** - يجب إعادة تشغيل الخادم
2. **تم تحسين البحث عن البيانات** - يدعم هياكل بيانات متعددة
3. **إضافة logs للتشخيص** - يمكن إزالتها في الإنتاج
4. **الكود متوافق مع البيانات القديمة والجديدة**

### 4. ✅ التوجيه إلى الصفحة الرئيسية بعد تأكيد الاستلام
**المشكلة:** العميل يبقى في صفحة تتبع الطلب بعد تأكيد الاستلام

**الحل:** تم إضافة التوجيه التلقائي إلى الصفحة الرئيسية:
```dart
// التوجيه إلى الصفحة الرئيسية بعد ثانيتين
Future.delayed(const Duration(seconds: 2), () {
  if (mounted) {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => HomeScreen(
          isDark: widget.isDark,
          onToggleDarkMode: widget.onToggleDarkMode,
          onLogout: () {},
        ),
      ),
      (route) => false,
    );
  }
});
```

## الخطوات التالية:

1. إعادة تشغيل الخادم لتطبيق تغييرات enum
2. اختبار تحديث حالة الطلب والتأكد من بقاء المبلغ
3. اختبار تعيين السائق وظهور بياناته
4. اختبار تأكيد استلام الطلب والتوجيه للصفحة الرئيسية
