# دليل استخدام صفحة تتبع الطلب الاحترافية

## نظرة عامة

تم تحديث صفحة `OrderTrackingScreen` لتصبح صفحة تتبع احترافية مع البيانات الحقيقية للعميل. الآن عندما يقبل العميل عرضاً من الصيدلية، سيتم توجيهه إلى صفحة التتبع الاحترافية التي تعرض:

## ✨ المميزات الجديدة

### 🔄 **التحديث الفوري**
- تحديث البيانات كل 10 ثواني من الخادم
- عندما تضغط الصيدلية على "بدء التجهيز" يظهر للعميل خلال 10 ثواني
- إشعارات فورية عند تغيير حالة الطلب

### 📱 **واجهة احترافية**
- تصميم عصري مع تدرجات لونية
- مسار تتبع أفقي يوضح مراحل الطلب
- أيقونات ملونة لكل مرحلة
- معلومات مفصلة عن الطلب والسائق

### 📞 **التواصل مع السائق**
- زر اتصال مباشر بالسائق
- رسائل سريعة ومخصصة
- معلومات السائق والمركبة

## 🔧 التحديثات المطبقة

### 1. في `offers_screen.dart`
```dart
// الانتقال لتتبع الطلب الاحترافي
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (_) => OrderTrackingScreen(
      orderId: widget.requestId, // تمرير ID الطلب للتتبع الحقيقي
      isDark: widget.isDark,
      onToggleDarkMode: widget.onToggleDarkMode,
      pharmacyAddress: result['data']['pharmacyName'] ?? 'الصيدلية',
      userAddress: 'موقعك الحالي',
      order: result['data'], // تمرير بيانات الطلب
    ),
  ),
);
```

### 2. في `home_screen.dart`
```dart
OrderTrackingScreen(
  orderId: 'DEMO-ORDER-123', // طلب تجريبي للعرض
  isDark: _isDarkMode,
  onToggleDarkMode: _toggleDarkMode,
  pharmacyAddress: 'صيدلية النهضة - شارع الجمهورية',
  userAddress: 'موقعك الحالي',
),
```

### 3. في `OrderService`
```dart
/// الحصول على طلب محدد بـ ID (للعميل)
static Future<Map<String, dynamic>> getOrderById({
  required String orderId,
}) async {
  // جلب طلب محدد من الخادم باستخدام ID
}
```

## 🎯 كيفية عمل التتبع الاحترافي

### للعميل:
1. **طلب الدواء**: العميل يطلب دواء من `RequestMedicineScreen`
2. **استلام العروض**: العميل يرى العروض في `OffersScreen`
3. **قبول العرض**: عند قبول عرض، ينتقل إلى `OrderTrackingScreen` الاحترافية
4. **التتبع المباشر**: يحصل على تحديثات فورية كل 10 ثواني

### للصيدلية:
1. **استلام الطلب**: الصيدلية ترى الطلب في `HomeScreenPharmacy`
2. **إرسال عرض**: تقدم عرض بالسعر والوقت
3. **بدء التجهيز**: عند الضغط على "بدء التجهيز" في `AcceptedOrdersScreen`
4. **تحديث فوري**: العميل يحصل على الإشعار خلال 10 ثواني

## 📊 حالات الطلب

| الحالة | الوصف | اللون | الأيقونة |
|--------|--------|-------|---------|
| `pending` | في انتظار الموافقة | رمادي | `info` |
| `confirmed` | تم تأكيد الطلب | أزرق | `check_circle` |
| `preparing` | جاري التجهيز | برتقالي | `kitchen` |
| `ready` | جاهز للاستلام | بنفسجي | `inventory` |
| `driver_assigned` | تم تعيين سائق | تركوازي | `person` |
| `out_for_delivery` | في الطريق إليك | أخضر فاتح | `local_shipping` |
| `delivered` | تم التوصيل | أخضر | `done_all` |
| `cancelled` | تم الإلغاء | أحمر | `cancel` |

## 🔗 الربط بين الصفحات

```
RequestMedicineScreen → OffersScreen → OrderTrackingScreen (احترافية)
                                            ↑
                                    تحديث كل 10 ثواني
                                            ↑
                                    AcceptedOrdersScreen (صيدلية)
```

## 🚀 النتيجة النهائية

الآن عندما يقبل العميل عرضاً من الصيدلية:
1. ✅ ينتقل إلى صفحة التتبع الاحترافية
2. ✅ يحصل على تحديثات فورية من الخادم
3. ✅ يرى إشعارات ملونة عند تغيير الحالة
4. ✅ يمكنه التواصل مع السائق
5. ✅ يرى معلومات مفصلة عن الطلب

هذا يحقق التجربة الاحترافية المطلوبة للعميل! 🎉
