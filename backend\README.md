# 🏥 Pharmacy Backend API

Backend API for the Pharmacy Management System built with Node.js, Express, and MongoDB.

## 🚀 Features

### 🔐 **Authentication & Authorization**
- User and pharmacy registration with email verification
- Secure login with JWT tokens
- Password reset functionality
- Account lockout after failed attempts
- Refresh token mechanism
- Role-based access control (user, pharmacy, admin)

### 👤 **User Management**
- Profile management
- Account activation/deactivation
- User statistics and analytics
- Egyptian governorates support

### 🏥 **Pharmacy Management**
- Pharmacy registration and verification
- Location-based pharmacy discovery
- Working hours and services management
- Rating and review system
- Dashboard with analytics

### 💊 **Medicine Management**
- Comprehensive medicine database
- Category and form classification
- Search with filters and sorting
- Prescription requirements
- Price management

### 📦 **Inventory Management**
- Stock level tracking
- Batch and expiry date management
- Low stock and expiry alerts
- Movement history
- Automatic stock adjustments

### 🛒 **Order Management**
- Order creation and tracking
- Status updates and notifications
- Delivery management
- Rating and feedback system
- Order statistics

### 📧 **Email Services**
- Email verification
- Password reset emails
- Welcome emails
- Arabic RTL email templates

### 🛡️ **Security Features**
- Rate limiting
- Helmet security headers
- Input validation and sanitization
- Password hashing with bcrypt
- Account lockout protection

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   - Copy `.env.example` to `.env`
   - Update the environment variables:
   ```env
   PORT=3000
   NODE_ENV=development
   MONGODB_URI=mongodb://localhost:27017/pharmacy_db
   JWT_SECRET=your_super_secret_jwt_key_here
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your_app_password
   ```

4. **Start MongoDB**
   ```bash
   # Using MongoDB locally
   mongod
   
   # Or using Docker
   docker run -d -p 27017:27017 --name mongodb mongo:latest
   ```

5. **Run the application**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## 📚 API Endpoints

### 🔐 Authentication Routes (`/api/auth`)

| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| POST | `/register` | Register new user | Public |
| POST | `/login` | User login | Public |
| POST | `/verify-email` | Verify email address | Public |
| POST | `/resend-verification` | Resend verification email | Public |
| POST | `/forgot-password` | Request password reset | Public |
| POST | `/reset-password` | Reset password with token | Public |
| POST | `/change-password` | Change password | Private |
| POST | `/logout` | Logout user | Private |
| POST | `/logout-all` | Logout from all devices | Private |
| POST | `/refresh-token` | Refresh access token | Public |
| GET | `/me` | Get current user | Private |

### 👤 User Routes (`/api/users`)

| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| GET | `/profile` | Get user profile | Private |
| PUT | `/profile` | Update user profile | Private |
| DELETE | `/account` | Delete user account | Private |
| GET | `/` | Get all users | Admin |
| GET | `/:id` | Get user by ID | Admin |
| PUT | `/:id` | Update user | Admin |
| DELETE | `/:id` | Delete user | Admin |
| GET | `/stats/overview` | User statistics | Admin |

### 🏥 Pharmacy Routes (`/api/pharmacy`)

| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| POST | `/register` | Register new pharmacy | Public |
| POST | `/login` | Pharmacy login | Public |
| GET | `/` | Get all pharmacies (with location filter) | Public |
| GET | `/:id` | Get pharmacy by ID | Public |
| GET | `/dashboard` | Get pharmacy dashboard data | Private/Pharmacy |

### 💊 Medicine Routes (`/api/medicines`)

| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| GET | `/` | Search medicines with filters | Public |
| GET | `/:id` | Get medicine by ID | Public |
| GET | `/categories/list` | Get medicine categories | Public |
| POST | `/` | Add new medicine | Admin |
| PUT | `/:id` | Update medicine | Admin |
| DELETE | `/:id` | Delete medicine | Admin |
| GET | `/stats/overview` | Medicine statistics | Admin |

### 🛒 Order Routes (`/api/orders`)

| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| GET | `/` | Get user orders | Private/User |
| GET | `/:id` | Get order by ID | Private |
| POST | `/` | Create new order | Private/User |
| PUT | `/:id/status` | Update order status | Private/Pharmacy |
| PUT | `/:id/cancel` | Cancel order | Private |
| PUT | `/:id/rate` | Rate order | Private/User |

### 📦 Inventory Routes (`/api/inventory`)

| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| GET | `/` | Get pharmacy inventory | Private/Pharmacy |
| GET | `/:id` | Get inventory item by ID | Private/Pharmacy |
| POST | `/` | Add medicine to inventory | Private/Pharmacy |
| PUT | `/:id` | Update inventory item | Private/Pharmacy |
| DELETE | `/:id` | Delete inventory item | Private/Pharmacy |
| GET | `/stats/overview` | Inventory statistics | Private/Pharmacy |
| GET | `/movements` | Get movement history | Private/Pharmacy |

## Request/Response Examples

### User Registration
```bash
POST /api/auth/register
Content-Type: application/json

{
  "name": "أحمد محمد",
  "email": "<EMAIL>",
  "phone": "01234567890",
  "password": "password123",
  "address": "شارع النيل، المعادي، القاهرة",
  "governorate": "القاهرة"
}
```

**Response:**
```json
{
  "success": true,
  "message": "تم إنشاء الحساب بنجاح. يرجى تأكيد بريدك الإلكتروني",
  "data": {
    "user": {
      "_id": "...",
      "name": "أحمد محمد",
      "email": "<EMAIL>",
      "phone": "01234567890",
      "address": "شارع النيل، المعادي، القاهرة",
      "governorate": "القاهرة",
      "role": "user",
      "isEmailVerified": false,
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z"
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
  }
}
```

### User Login
```bash
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Email Verification
```bash
POST /api/auth/verify-email
Content-Type: application/json

{
  "token": "verification_token_from_email"
}
```

## Error Handling

The API uses consistent error response format:

```json
{
  "success": false,
  "message": "رسالة الخطأ باللغة العربية",
  "errors": ["تفاصيل إضافية عن الخطأ"]
}
```

## Security Features

- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Account Lockout**: 5 failed login attempts lock account for 15 minutes
- **Password Requirements**: Minimum 6 characters with letters and numbers
- **JWT Expiration**: Access tokens expire in 7 days, refresh tokens in 30 days
- **Input Validation**: All inputs are validated and sanitized
- **CORS Protection**: Configured for specific frontend origins

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 3000 |
| `NODE_ENV` | Environment mode | development |
| `MONGODB_URI` | MongoDB connection string | - |
| `JWT_SECRET` | JWT signing secret | - |
| `JWT_EXPIRE` | JWT expiration time | 7d |
| `EMAIL_HOST` | SMTP host | smtp.gmail.com |
| `EMAIL_PORT` | SMTP port | 587 |
| `EMAIL_USER` | SMTP username | - |
| `EMAIL_PASS` | SMTP password | - |
| `BCRYPT_ROUNDS` | Password hashing rounds | 12 |
| `MAX_LOGIN_ATTEMPTS` | Max failed login attempts | 5 |
| `LOCKOUT_TIME` | Account lockout time (minutes) | 15 |

## Development

```bash
# Install dependencies
npm install

# Run in development mode with auto-reload
npm run dev

# Run tests
npm test

# Check code style
npm run lint
```

## Production Deployment

1. Set `NODE_ENV=production`
2. Use a process manager like PM2
3. Set up reverse proxy with Nginx
4. Use MongoDB Atlas for database
5. Configure proper SMTP service
6. Set strong JWT secrets
7. Enable HTTPS

## License

MIT License
