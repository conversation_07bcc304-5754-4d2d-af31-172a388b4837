const { body, validationResult } = require('express-validator');

// Handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    console.log('❌ Validation errors:', {
      body: req.body,
      errors: errors.array()
    });
    const errorMessages = errors.array().map(error => error.msg);
    return res.status(400).json({
      success: false,
      message: 'بيانات غير صحيحة',
      errors: errorMessages
    });
  }
  next();
};

// User registration validation
const validateUserRegistration = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('الاسم يجب أن يكون بين 2 و 100 حرف')
    .matches(/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\w]+$/)
    .withMessage('الاسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط'),

  body('email')
    .isEmail()
    .withMessage('يرجى إدخال بريد إلكتروني صحيح')
    .normalizeEmail()
    .toLowerCase(),

  body('phone')
    .matches(/^[0-9]{10,15}$/)
    .withMessage('رقم الهاتف يجب أن يكون بين 10 و 15 رقم'),

  body('password')
    .isLength({ min: 6 })
    .withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
    .withMessage('كلمة المرور يجب أن تحتوي على أحرف وأرقام'),

  body('address')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('العنوان يجب أن يكون بين 10 و 500 حرف'),

  body('governorate')
    .isIn([
      'القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'البحر الأحمر', 'البحيرة',
      'الفيوم', 'الغربية', 'الإسماعيلية', 'المنوفية', 'المنيا', 'القليوبية',
      'الوادي الجديد', 'السويس', 'أسوان', 'أسيوط', 'بني سويف', 'بورسعيد',
      'دمياط', 'الشرقية', 'جنوب سيناء', 'كفر الشيخ', 'مطروح', 'الأقصر',
      'قنا', 'شمال سيناء', 'سوهاج'
    ])
    .withMessage('يرجى اختيار محافظة صحيحة'),

  handleValidationErrors
];

// User login validation
const validateUserLogin = [
  body('email')
    .isEmail()
    .withMessage('يرجى إدخال بريد إلكتروني صحيح')
    .normalizeEmail()
    .toLowerCase(),

  body('password')
    .notEmpty()
    .withMessage('كلمة المرور مطلوبة'),

  handleValidationErrors
];

// Password reset request validation
const validatePasswordResetRequest = [
  body('email')
    .isEmail()
    .withMessage('يرجى إدخال بريد إلكتروني صحيح')
    .normalizeEmail()
    .toLowerCase(),

  handleValidationErrors
];

// Password reset validation
const validatePasswordReset = [
  body('token')
    .notEmpty()
    .withMessage('رمز إعادة التعيين مطلوب'),

  body('password')
    .isLength({ min: 6 })
    .withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
    .withMessage('كلمة المرور يجب أن تحتوي على أحرف وأرقام'),

  handleValidationErrors
];

// Change password validation
const validateChangePassword = [
  body('currentPassword')
    .notEmpty()
    .withMessage('كلمة المرور الحالية مطلوبة'),

  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل')
    .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
    .withMessage('كلمة المرور يجب أن تحتوي على أحرف وأرقام'),

  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('تأكيد كلمة المرور غير متطابق');
      }
      return true;
    }),

  handleValidationErrors
];

// Update profile validation
const validateUpdateProfile = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('الاسم يجب أن يكون بين 2 و 100 حرف')
    .matches(/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\w]+$/)
    .withMessage('الاسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط'),

  body('phone')
    .optional()
    .matches(/^[0-9]{10,15}$/)
    .withMessage('رقم الهاتف يجب أن يكون بين 10 و 15 رقم'),

  body('address')
    .optional()
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('العنوان يجب أن يكون بين 10 و 500 حرف'),

  body('governorate')
    .optional()
    .isIn([
      'القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'البحر الأحمر', 'البحيرة',
      'الفيوم', 'الغربية', 'الإسماعيلية', 'المنوفية', 'المنيا', 'القليوبية',
      'الوادي الجديد', 'السويس', 'أسوان', 'أسيوط', 'بني سويف', 'بورسعيد',
      'دمياط', 'الشرقية', 'جنوب سيناء', 'كفر الشيخ', 'مطروح', 'الأقصر',
      'قنا', 'شمال سيناء', 'سوهاج'
    ])
    .withMessage('يرجى اختيار محافظة صحيحة'),

  handleValidationErrors
];

// Email verification validation
const validateEmailVerification = [
  body('token')
    .notEmpty()
    .withMessage('رمز التحقق مطلوب'),

  handleValidationErrors
];

// Resend verification email validation
const validateResendVerification = [
  body('email')
    .isEmail()
    .withMessage('يرجى إدخال بريد إلكتروني صحيح')
    .normalizeEmail()
    .toLowerCase(),

  handleValidationErrors
];

module.exports = {
  validateUserRegistration,
  validateUserLogin,
  validatePasswordResetRequest,
  validatePasswordReset,
  validateChangePassword,
  validateUpdateProfile,
  validateEmailVerification,
  validateResendVerification,
  handleValidationErrors
};
