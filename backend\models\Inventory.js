const mongoose = require('mongoose');

const inventorySchema = new mongoose.Schema({
  pharmacy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Pharmacy',
    required: [true, 'الصيدلية مطلوبة']
  },
  medicine: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Medicine',
    required: [true, 'الدواء مطلوب']
  },
  stock: {
    currentQuantity: {
      type: Number,
      required: [true, 'الكمية الحالية مطلوبة'],
      min: [0, 'الكمية لا يمكن أن تكون سالبة']
    },
    minimumQuantity: {
      type: Number,
      default: 10,
      min: [0, 'الحد الأدنى لا يمكن أن يكون سالب']
    },
    maximumQuantity: {
      type: Number,
      default: 1000
    },
    reservedQuantity: {
      type: Number,
      default: 0,
      min: [0, 'الكمية المحجوزة لا يمكن أن تكون سالبة']
    }
  },
  pricing: {
    costPrice: {
      type: Number,
      required: [true, 'سعر التكلفة مطلوب'],
      min: [0, 'سعر التكلفة لا يمكن أن يكون سالب']
    },
    sellingPrice: {
      type: Number,
      required: [true, 'سعر البيع مطلوب'],
      min: [0, 'سعر البيع لا يمكن أن يكون سالب']
    },
    discountPrice: {
      type: Number,
      default: 0,
      min: [0, 'سعر الخصم لا يمكن أن يكون سالب']
    },
    profitMargin: Number // نسبة الربح
  },
  batch: {
    batchNumber: String,
    manufacturingDate: Date,
    expiryDate: {
      type: Date,
      required: [true, 'تاريخ انتهاء الصلاحية مطلوب']
    },
    supplier: String
  },
  location: {
    shelf: String,
    section: String,
    position: String
  },
  status: {
    type: String,
    enum: ['available', 'low_stock', 'out_of_stock', 'expired', 'discontinued'],
    default: 'available'
  },
  alerts: {
    lowStockAlert: { type: Boolean, default: false },
    expiryAlert: { type: Boolean, default: false },
    lastAlertSent: Date
  },
  sales: {
    totalSold: { type: Number, default: 0 },
    lastSaleDate: Date,
    averageMonthlySales: { type: Number, default: 0 }
  },
  movements: [{
    type: {
      type: String,
      enum: ['purchase', 'sale', 'return', 'adjustment', 'transfer', 'expired'],
      required: true
    },
    quantity: {
      type: Number,
      required: true
    },
    previousQuantity: Number,
    newQuantity: Number,
    reason: String,
    reference: String, // رقم الفاتورة أو المرجع
    date: {
      type: Date,
      default: Date.now
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes
inventorySchema.index({ pharmacy: 1, medicine: 1 }, { unique: true });
inventorySchema.index({ pharmacy: 1, status: 1 });
inventorySchema.index({ 'batch.expiryDate': 1 });
inventorySchema.index({ 'stock.currentQuantity': 1 });
inventorySchema.index({ isActive: 1 });

// Virtual for available quantity (current - reserved)
inventorySchema.virtual('availableQuantity').get(function() {
  return Math.max(0, this.stock.currentQuantity - this.stock.reservedQuantity);
});

// Virtual for days until expiry
inventorySchema.virtual('daysUntilExpiry').get(function() {
  if (this.batch.expiryDate) {
    const now = new Date();
    const expiry = new Date(this.batch.expiryDate);
    const diffTime = expiry - now;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  return null;
});

// Virtual for profit margin calculation
inventorySchema.virtual('calculatedProfitMargin').get(function() {
  if (this.pricing.costPrice > 0) {
    return ((this.pricing.sellingPrice - this.pricing.costPrice) / this.pricing.costPrice) * 100;
  }
  return 0;
});

// Pre-save middleware to update status and profit margin
inventorySchema.pre('save', function(next) {
  // Update profit margin
  if (this.pricing.costPrice > 0) {
    this.pricing.profitMargin = ((this.pricing.sellingPrice - this.pricing.costPrice) / this.pricing.costPrice) * 100;
  }
  
  // Update status based on quantity and expiry
  if (this.stock.currentQuantity <= 0) {
    this.status = 'out_of_stock';
  } else if (this.stock.currentQuantity <= this.stock.minimumQuantity) {
    this.status = 'low_stock';
  } else if (this.daysUntilExpiry <= 0) {
    this.status = 'expired';
  } else {
    this.status = 'available';
  }
  
  // Set alerts
  this.alerts.lowStockAlert = this.stock.currentQuantity <= this.stock.minimumQuantity;
  this.alerts.expiryAlert = this.daysUntilExpiry <= 30 && this.daysUntilExpiry > 0;
  
  next();
});

// Instance method to add stock movement
inventorySchema.methods.addMovement = function(type, quantity, reason, reference, userId) {
  const previousQuantity = this.stock.currentQuantity;
  let newQuantity;
  
  switch (type) {
    case 'purchase':
    case 'return':
      newQuantity = previousQuantity + Math.abs(quantity);
      break;
    case 'sale':
    case 'expired':
      newQuantity = Math.max(0, previousQuantity - Math.abs(quantity));
      break;
    case 'adjustment':
      newQuantity = quantity; // Direct quantity for adjustments
      break;
    case 'transfer':
      newQuantity = Math.max(0, previousQuantity - Math.abs(quantity));
      break;
    default:
      throw new Error('نوع الحركة غير صحيح');
  }
  
  this.movements.push({
    type: type,
    quantity: type === 'adjustment' ? quantity - previousQuantity : quantity,
    previousQuantity: previousQuantity,
    newQuantity: newQuantity,
    reason: reason,
    reference: reference,
    user: userId
  });
  
  this.stock.currentQuantity = newQuantity;
  
  // Update sales data for sale movements
  if (type === 'sale') {
    this.sales.totalSold += Math.abs(quantity);
    this.sales.lastSaleDate = new Date();
  }
  
  return this.save();
};

// Instance method to reserve stock
inventorySchema.methods.reserveStock = function(quantity) {
  if (this.availableQuantity < quantity) {
    throw new Error('الكمية المطلوبة غير متوفرة');
  }
  
  this.stock.reservedQuantity += quantity;
  return this.save();
};

// Instance method to release reserved stock
inventorySchema.methods.releaseReservedStock = function(quantity) {
  this.stock.reservedQuantity = Math.max(0, this.stock.reservedQuantity - quantity);
  return this.save();
};

// Static method to get low stock items
inventorySchema.statics.getLowStockItems = function(pharmacyId) {
  return this.find({
    pharmacy: pharmacyId,
    status: 'low_stock',
    isActive: true
  }).populate('medicine', 'name manufacturer form strength');
};

// Static method to get expiring items
inventorySchema.statics.getExpiringItems = function(pharmacyId, days = 30) {
  const expiryDate = new Date();
  expiryDate.setDate(expiryDate.getDate() + days);
  
  return this.find({
    pharmacy: pharmacyId,
    'batch.expiryDate': { $lte: expiryDate, $gte: new Date() },
    isActive: true
  }).populate('medicine', 'name manufacturer form strength');
};

// Static method to get inventory statistics
inventorySchema.statics.getInventoryStats = function(pharmacyId) {
  return this.aggregate([
    { $match: { pharmacy: mongoose.Types.ObjectId(pharmacyId), isActive: true } },
    {
      $group: {
        _id: null,
        totalItems: { $sum: 1 },
        totalValue: { $sum: { $multiply: ['$stock.currentQuantity', '$pricing.costPrice'] } },
        lowStockItems: {
          $sum: { $cond: [{ $eq: ['$status', 'low_stock'] }, 1, 0] }
        },
        outOfStockItems: {
          $sum: { $cond: [{ $eq: ['$status', 'out_of_stock'] }, 1, 0] }
        },
        expiredItems: {
          $sum: { $cond: [{ $eq: ['$status', 'expired'] }, 1, 0] }
        },
        totalQuantity: { $sum: '$stock.currentQuantity' },
        averageProfitMargin: { $avg: '$pricing.profitMargin' }
      }
    }
  ]);
};

// Static method to get top selling items
inventorySchema.statics.getTopSellingItems = function(pharmacyId, limit = 10) {
  return this.find({
    pharmacy: pharmacyId,
    isActive: true
  })
  .sort({ 'sales.totalSold': -1 })
  .limit(limit)
  .populate('medicine', 'name manufacturer form strength');
};

// Static method to get movement history
inventorySchema.statics.getMovementHistory = function(pharmacyId, startDate, endDate) {
  return this.aggregate([
    { $match: { pharmacy: mongoose.Types.ObjectId(pharmacyId) } },
    { $unwind: '$movements' },
    {
      $match: {
        'movements.date': {
          $gte: startDate,
          $lte: endDate
        }
      }
    },
    {
      $lookup: {
        from: 'medicines',
        localField: 'medicine',
        foreignField: '_id',
        as: 'medicineInfo'
      }
    },
    { $unwind: '$medicineInfo' },
    {
      $project: {
        'movements.type': 1,
        'movements.quantity': 1,
        'movements.reason': 1,
        'movements.reference': 1,
        'movements.date': 1,
        'medicineInfo.name': 1,
        'medicineInfo.manufacturer': 1
      }
    },
    { $sort: { 'movements.date': -1 } }
  ]);
};

module.exports = mongoose.model('Inventory', inventorySchema);
