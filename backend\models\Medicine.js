const mongoose = require('mongoose');

const medicineSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'اسم الدواء مطلوب'],
    trim: true,
    maxlength: [200, 'اسم الدواء يجب أن يكون أقل من 200 حرف']
  },
  genericName: {
    type: String,
    trim: true,
    maxlength: [200, 'الاسم العلمي يجب أن يكون أقل من 200 حرف']
  },
  barcode: {
    type: String,
    unique: true,
    sparse: true
  },
  manufacturer: {
    type: String,
    required: [true, 'الشركة المصنعة مطلوبة'],
    trim: true
  },
  category: {
    type: String,
    required: [true, 'فئة الدواء مطلوبة'],
    enum: [
      'مسكنات', 'مضادات حيوية', 'أدوية القلب', 'أدوية الضغط', 'أدوية السكري',
      'أدوية الجهاز الهضمي', 'أدوية الجهاز التنفسي', 'أدوية الأطفال',
      'فيتامينات ومكملات', 'أدوية الجلدية', 'أدوية العيون', 'أدوية الأذن',
      'أدوية النساء والتوليد', 'أدوية الأعصاب', 'أدوية نفسية', 'أدوية السرطان',
      'أدوية المناعة', 'أدوية العظام والمفاصل', 'أخرى'
    ]
  },
  form: {
    type: String,
    required: [true, 'شكل الدواء مطلوب'],
    enum: [
      'أقراص', 'كبسولات', 'شراب', 'حقن', 'مرهم', 'كريم', 'قطرة عين',
      'قطرة أذن', 'بخاخ', 'لبوس', 'جل', 'محلول', 'مسحوق', 'أخرى'
    ]
  },
  strength: {
    type: String,
    required: [true, 'تركيز الدواء مطلوب']
  },
  packSize: {
    type: String,
    required: [true, 'حجم العبوة مطلوب']
  },
  description: {
    type: String,
    maxlength: [1000, 'الوصف يجب أن يكون أقل من 1000 حرف']
  },
  activeIngredients: [{
    name: String,
    concentration: String
  }],
  indications: [String], // دواعي الاستعمال
  contraindications: [String], // موانع الاستعمال
  sideEffects: [String], // الآثار الجانبية
  dosage: {
    adults: String,
    children: String,
    elderly: String,
    special: String
  },
  storage: {
    temperature: String,
    conditions: String,
    shelfLife: String // مدة الصلاحية
  },
  prescriptionRequired: {
    type: Boolean,
    default: false
  },
  controlledSubstance: {
    type: Boolean,
    default: false
  },
  images: [{
    url: String,
    alt: String,
    isPrimary: { type: Boolean, default: false }
  }],
  pricing: {
    basePrice: {
      type: Number,
      required: [true, 'السعر الأساسي مطلوب'],
      min: [0, 'السعر لا يمكن أن يكون سالب']
    },
    currency: {
      type: String,
      default: 'EGP'
    },
    discountPercentage: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    finalPrice: Number
  },
  availability: {
    inStock: {
      type: Boolean,
      default: true
    },
    stockLevel: {
      type: String,
      enum: ['متوفر', 'قليل', 'غير متوفر'],
      default: 'متوفر'
    },
    estimatedRestockDate: Date
  },
  tags: [String], // للبحث والفلترة
  isActive: {
    type: Boolean,
    default: true
  },
  isApproved: {
    type: Boolean,
    default: false
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedAt: Date,
  rating: {
    average: { type: Number, default: 0, min: 0, max: 5 },
    count: { type: Number, default: 0 }
  },
  statistics: {
    viewCount: { type: Number, default: 0 },
    orderCount: { type: Number, default: 0 },
    favoriteCount: { type: Number, default: 0 }
  },
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String]
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
medicineSchema.index({ name: 'text', genericName: 'text', manufacturer: 'text' });
medicineSchema.index({ category: 1 });
medicineSchema.index({ form: 1 });
medicineSchema.index({ prescriptionRequired: 1 });
medicineSchema.index({ 'availability.inStock': 1 });
medicineSchema.index({ 'pricing.finalPrice': 1 });
medicineSchema.index({ isActive: 1, isApproved: 1 });
medicineSchema.index({ 'rating.average': -1 });
medicineSchema.index({ createdAt: -1 });
medicineSchema.index({ barcode: 1 });

// Virtual for discounted price
medicineSchema.virtual('discountedPrice').get(function() {
  if (this.pricing.discountPercentage > 0) {
    return this.pricing.basePrice * (1 - this.pricing.discountPercentage / 100);
  }
  return this.pricing.basePrice;
});

// Pre-save middleware to calculate final price
medicineSchema.pre('save', function(next) {
  if (this.pricing.discountPercentage > 0) {
    this.pricing.finalPrice = this.pricing.basePrice * (1 - this.pricing.discountPercentage / 100);
  } else {
    this.pricing.finalPrice = this.pricing.basePrice;
  }
  next();
});

// Static method for text search
medicineSchema.statics.searchMedicines = function(query, options = {}) {
  const {
    category,
    form,
    prescriptionRequired,
    inStock,
    minPrice,
    maxPrice,
    sortBy = 'relevance',
    page = 1,
    limit = 20
  } = options;

  const searchQuery = {
    isActive: true,
    isApproved: true
  };

  // Text search
  if (query) {
    searchQuery.$text = { $search: query };
  }

  // Filters
  if (category) searchQuery.category = category;
  if (form) searchQuery.form = form;
  if (prescriptionRequired !== undefined) searchQuery.prescriptionRequired = prescriptionRequired;
  if (inStock !== undefined) searchQuery['availability.inStock'] = inStock;
  
  if (minPrice || maxPrice) {
    searchQuery['pricing.finalPrice'] = {};
    if (minPrice) searchQuery['pricing.finalPrice'].$gte = minPrice;
    if (maxPrice) searchQuery['pricing.finalPrice'].$lte = maxPrice;
  }

  // Sorting
  let sort = {};
  switch (sortBy) {
    case 'price_low':
      sort = { 'pricing.finalPrice': 1 };
      break;
    case 'price_high':
      sort = { 'pricing.finalPrice': -1 };
      break;
    case 'rating':
      sort = { 'rating.average': -1 };
      break;
    case 'newest':
      sort = { createdAt: -1 };
      break;
    case 'popular':
      sort = { 'statistics.orderCount': -1 };
      break;
    default:
      if (query) {
        sort = { score: { $meta: 'textScore' } };
      } else {
        sort = { createdAt: -1 };
      }
  }

  const skip = (page - 1) * limit;

  return this.find(searchQuery)
    .sort(sort)
    .skip(skip)
    .limit(limit)
    .populate('approvedBy', 'name');
};

// Static method to get medicine statistics
medicineSchema.statics.getStatistics = function() {
  return this.aggregate([
    {
      $group: {
        _id: null,
        totalMedicines: { $sum: 1 },
        activeMedicines: {
          $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] }
        },
        approvedMedicines: {
          $sum: { $cond: [{ $eq: ['$isApproved', true] }, 1, 0] }
        },
        inStockMedicines: {
          $sum: { $cond: [{ $eq: ['$availability.inStock', true] }, 1, 0] }
        },
        averagePrice: { $avg: '$pricing.finalPrice' },
        totalViews: { $sum: '$statistics.viewCount' },
        totalOrders: { $sum: '$statistics.orderCount' }
      }
    }
  ]);
};

// Static method to get category statistics
medicineSchema.statics.getCategoryStats = function() {
  return this.aggregate([
    {
      $match: { isActive: true, isApproved: true }
    },
    {
      $group: {
        _id: '$category',
        count: { $sum: 1 },
        averagePrice: { $avg: '$pricing.finalPrice' },
        inStock: {
          $sum: { $cond: [{ $eq: ['$availability.inStock', true] }, 1, 0] }
        }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);
};

// Instance method to increment view count
medicineSchema.methods.incrementViewCount = function() {
  return this.updateOne({ $inc: { 'statistics.viewCount': 1 } });
};

// Instance method to increment order count
medicineSchema.methods.incrementOrderCount = function(quantity = 1) {
  return this.updateOne({ $inc: { 'statistics.orderCount': quantity } });
};

// Instance method to update rating
medicineSchema.methods.updateRating = function(newRating) {
  const currentTotal = this.rating.average * this.rating.count;
  const newCount = this.rating.count + 1;
  const newAverage = (currentTotal + newRating) / newCount;
  
  return this.updateOne({
    'rating.average': Math.round(newAverage * 10) / 10,
    'rating.count': newCount
  });
};

module.exports = mongoose.model('Medicine', medicineSchema);
