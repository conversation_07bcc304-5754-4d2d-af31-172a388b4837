const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const pharmacySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'اسم الصيدلية مطلوب'],
    trim: true,
    maxlength: [100, 'اسم الصيدلية يجب أن يكون أقل من 100 حرف']
  },
  email: {
    type: String,
    required: [true, 'البريد الإلكتروني مطلوب'],
    unique: true,
    lowercase: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'يرجى إدخال بريد إلكتروني صحيح'
    ]
  },
  phone: {
    type: String,
    required: [true, 'رقم الهاتف مطلوب'],
    match: [/^[0-9]{10,15}$/, 'يرجى إدخال رقم هاتف صحيح']
  },
  password: {
    type: String,
    required: [true, 'كلمة المرور مطلوبة'],
    minlength: [6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'],
    select: false
  },
  licenseNumber: {
    type: String,
    required: [true, 'رقم الترخيص مطلوب'],
    unique: true
  },
  address: {
    street: {
      type: String,
      required: [true, 'عنوان الشارع مطلوب']
    },
    city: {
      type: String,
      required: [true, 'المدينة مطلوبة']
    },
    governorate: {
      type: String,
      required: [true, 'المحافظة مطلوبة'],
      enum: [
        'القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'البحر الأحمر', 'البحيرة',
        'الفيوم', 'الغربية', 'الإسماعيلية', 'المنوفية', 'المنيا', 'القليوبية',
        'الوادي الجديد', 'السويس', 'أسوان', 'أسيوط', 'بني سويف', 'بورسعيد',
        'دمياط', 'الشرقية', 'جنوب سيناء', 'كفر الشيخ', 'مطروح', 'الأقصر',
        'قنا', 'شمال سيناء', 'سوهاج'
      ]
    },
    postalCode: String
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      required: [true, 'الإحداثيات مطلوبة']
    }
  },
  workingHours: {
    monday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
    tuesday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
    wednesday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
    thursday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
    friday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
    saturday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
    sunday: { open: String, close: String, isOpen: { type: Boolean, default: false } }
  },
  services: {
    delivery: {
      available: { type: Boolean, default: true },
      fee: { type: Number, default: 0 },
      freeDeliveryMinimum: { type: Number, default: 100 },
      estimatedTime: { type: Number, default: 30 } // minutes
    },
    consultation: { type: Boolean, default: false },
    insurance: { type: Boolean, default: false },
    emergencyService: { type: Boolean, default: false }
  },
  rating: {
    average: { type: Number, default: 0, min: 0, max: 5 },
    count: { type: Number, default: 0 }
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  documents: {
    license: String, // URL to license document
    commercialRegister: String,
    taxCard: String
  },
  bankAccount: {
    bankName: String,
    accountNumber: String,
    iban: String
  },
  statistics: {
    totalOrders: { type: Number, default: 0 },
    completedOrders: { type: Number, default: 0 },
    cancelledOrders: { type: Number, default: 0 },
    totalRevenue: { type: Number, default: 0 },
    averageOrderValue: { type: Number, default: 0 },
    responseTime: { type: Number, default: 0 } // minutes
  },
  loginAttempts: {
    type: Number,
    default: 0
  },
  lockUntil: Date,
  refreshTokens: [{
    token: String,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  lastLogin: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create geospatial index
pharmacySchema.index({ location: '2dsphere' });

// Other indexes
pharmacySchema.index({ email: 1 });
pharmacySchema.index({ licenseNumber: 1 });
pharmacySchema.index({ 'address.governorate': 1 });
pharmacySchema.index({ isActive: 1, isVerified: 1 });
pharmacySchema.index({ 'rating.average': -1 });

// Virtual for account lock status
pharmacySchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Pre-save middleware to hash password
pharmacySchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS) || 12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Instance method to check password
pharmacySchema.methods.comparePassword = async function(candidatePassword) {
  if (!this.password) return false;
  return await bcrypt.compare(candidatePassword, this.password);
};

// Instance method to handle failed login attempts
pharmacySchema.methods.incLoginAttempts = function() {
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5;
  const lockoutTime = (parseInt(process.env.LOCKOUT_TIME) || 15) * 60 * 1000;
  
  if (this.loginAttempts + 1 >= maxAttempts && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + lockoutTime };
  }
  
  return this.updateOne(updates);
};

// Instance method to reset login attempts
pharmacySchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 }
  });
};

// Instance method to calculate distance from a point
pharmacySchema.methods.getDistanceFrom = function(longitude, latitude) {
  const [pharmLng, pharmLat] = this.location.coordinates;
  const R = 6371; // Earth's radius in kilometers
  
  const dLat = (latitude - pharmLat) * Math.PI / 180;
  const dLng = (longitude - pharmLng) * Math.PI / 180;
  
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(pharmLat * Math.PI / 180) * Math.cos(latitude * Math.PI / 180) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c; // Distance in kilometers
};

// Static method to find nearby pharmacies
pharmacySchema.statics.findNearby = function(longitude, latitude, maxDistance = 10) {
  return this.aggregate([
    {
      $geoNear: {
        near: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        distanceField: 'distance',
        maxDistance: maxDistance * 1000, // Convert to meters
        spherical: true,
        query: { isActive: true, isVerified: true }
      }
    },
    {
      $addFields: {
        distanceKm: { $divide: ['$distance', 1000] }
      }
    },
    {
      $sort: { distance: 1 }
    }
  ]);
};

// Static method to get pharmacy statistics
pharmacySchema.statics.getStatistics = function() {
  return this.aggregate([
    {
      $group: {
        _id: null,
        totalPharmacies: { $sum: 1 },
        activePharmacies: {
          $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] }
        },
        verifiedPharmacies: {
          $sum: { $cond: [{ $eq: ['$isVerified', true] }, 1, 0] }
        },
        averageRating: { $avg: '$rating.average' },
        totalOrders: { $sum: '$statistics.totalOrders' },
        totalRevenue: { $sum: '$statistics.totalRevenue' }
      }
    }
  ]);
};

module.exports = mongoose.model('Pharmacy', pharmacySchema);
