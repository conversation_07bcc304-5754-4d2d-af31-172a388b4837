const express = require('express');
const Pharmacy = require('../models/Pharmacy');
const User = require('../models/User');
const Order = require('../models/Order');
const { protect, authorize } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

const router = express.Router();

// Middleware للتحقق من صلاحيات الإدارة
const requireAdmin = (req, res, next) => {
  // للتطوير: أي مستخدم يمكنه الوصول
  // في الإنتاج: يجب إضافة نظام مصادقة للإدارة
  next();
};

// @desc    Get admin dashboard statistics
// @route   GET /api/admin/dashboard
// @access  Admin
router.get('/dashboard', requireAdmin, async (req, res) => {
  try {
    const [
      totalPharmacies,
      verifiedPharmacies,
      pendingPharmacies,
      totalUsers,
      totalOrders,
      recentPharmacies
    ] = await Promise.all([
      Pharmacy.countDocuments(),
      Pharmacy.countDocuments({ isVerified: true }),
      Pharmacy.countDocuments({ isVerified: false }),
      User.countDocuments(),
      Order.countDocuments(),
      Pharmacy.find({ isVerified: false })
        .select('name email phone licenseNumber createdAt')
        .sort({ createdAt: -1 })
        .limit(10)
    ]);

    res.json({
      success: true,
      data: {
        statistics: {
          pharmacies: {
            total: totalPharmacies,
            verified: verifiedPharmacies,
            pending: pendingPharmacies
          },
          users: totalUsers,
          orders: totalOrders
        },
        pendingPharmacies: recentPharmacies
      }
    });
  } catch (error) {
    console.error('Admin dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب بيانات لوحة الإدارة'
    });
  }
});

// @desc    Get all pharmacies for admin
// @route   GET /api/admin/pharmacies
// @access  Admin
router.get('/pharmacies', requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status, // 'verified', 'pending', 'all'
      search
    } = req.query;

    let query = {};
    
    if (status === 'verified') {
      query.isVerified = true;
    } else if (status === 'pending') {
      query.isVerified = false;
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { licenseNumber: { $regex: search, $options: 'i' } }
      ];
    }

    const pharmacies = await Pharmacy.find(query)
      .select('-password -refreshTokens')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Pharmacy.countDocuments(query);

    res.json({
      success: true,
      data: {
        pharmacies,
        pagination: {
          current: page,
          pages: Math.ceil(total / limit),
          total
        }
      }
    });
  } catch (error) {
    console.error('Get pharmacies error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الصيدليات'
    });
  }
});

// @desc    Verify/Activate pharmacy
// @route   PUT /api/admin/pharmacies/:id/verify
// @access  Admin
router.put('/pharmacies/:id/verify', requireAdmin, async (req, res) => {
  try {
    const { isVerified, isActive } = req.body;
    
    const pharmacy = await Pharmacy.findById(req.params.id);
    
    if (!pharmacy) {
      return res.status(404).json({
        success: false,
        message: 'الصيدلية غير موجودة'
      });
    }
    
    // تحديث حالة التفعيل
    if (typeof isVerified === 'boolean') {
      pharmacy.isVerified = isVerified;
    }
    
    if (typeof isActive === 'boolean') {
      pharmacy.isActive = isActive;
    }
    
    await pharmacy.save();
    
    const statusText = pharmacy.isVerified ? 'مفعلة' : 'غير مفعلة';
    const activeText = pharmacy.isActive ? 'نشطة' : 'معطلة';
    
    res.json({
      success: true,
      message: `تم تحديث حالة الصيدلية: ${statusText} و ${activeText}`,
      data: {
        pharmacy: {
          id: pharmacy._id,
          name: pharmacy.name,
          email: pharmacy.email,
          isVerified: pharmacy.isVerified,
          isActive: pharmacy.isActive
        }
      }
    });
  } catch (error) {
    console.error('Verify pharmacy error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث حالة الصيدلية'
    });
  }
});

// @desc    Get pharmacy details for admin
// @route   GET /api/admin/pharmacies/:id
// @access  Admin
router.get('/pharmacies/:id', requireAdmin, async (req, res) => {
  try {
    const pharmacy = await Pharmacy.findById(req.params.id)
      .select('-password -refreshTokens');
    
    if (!pharmacy) {
      return res.status(404).json({
        success: false,
        message: 'الصيدلية غير موجودة'
      });
    }
    
    // جلب إحصائيات الصيدلية
    const orderStats = await Order.aggregate([
      { $match: { pharmacy: pharmacy._id } },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          completedOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
          },
          totalRevenue: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, '$pricing.total', 0] }
          }
        }
      }
    ]);
    
    res.json({
      success: true,
      data: {
        pharmacy,
        statistics: orderStats[0] || {
          totalOrders: 0,
          completedOrders: 0,
          totalRevenue: 0
        }
      }
    });
  } catch (error) {
    console.error('Get pharmacy details error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب تفاصيل الصيدلية'
    });
  }
});

// @desc    Delete pharmacy
// @route   DELETE /api/admin/pharmacies/:id
// @access  Admin
router.delete('/pharmacies/:id', requireAdmin, async (req, res) => {
  try {
    const pharmacy = await Pharmacy.findById(req.params.id);
    
    if (!pharmacy) {
      return res.status(404).json({
        success: false,
        message: 'الصيدلية غير موجودة'
      });
    }
    
    await Pharmacy.findByIdAndDelete(req.params.id);
    
    res.json({
      success: true,
      message: `تم حذف الصيدلية: ${pharmacy.name}`
    });
  } catch (error) {
    console.error('Delete pharmacy error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف الصيدلية'
    });
  }
});

// @desc    Bulk verify pharmacies
// @route   PUT /api/admin/pharmacies/bulk-verify
// @access  Admin
router.put('/pharmacies/bulk-verify', requireAdmin, [
  body('pharmacyIds').isArray().withMessage('معرفات الصيدليات مطلوبة'),
  body('isVerified').isBoolean().withMessage('حالة التفعيل مطلوبة')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }
    
    const { pharmacyIds, isVerified } = req.body;
    
    const result = await Pharmacy.updateMany(
      { _id: { $in: pharmacyIds } },
      { isVerified, isActive: isVerified }
    );
    
    res.json({
      success: true,
      message: `تم تحديث ${result.modifiedCount} صيدلية`,
      data: {
        modifiedCount: result.modifiedCount
      }
    });
  } catch (error) {
    console.error('Bulk verify error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في التحديث المجمع'
    });
  }
});

module.exports = router;
