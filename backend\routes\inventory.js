const express = require('express');
const Inventory = require('../models/Inventory');
const Medicine = require('../models/Medicine');
const { protect, authorize } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

const router = express.Router();

// @desc    Get pharmacy inventory
// @route   GET /api/inventory
// @access  Private/Pharmacy
router.get('/', protect, authorize('pharmacy'), async (req, res) => {
  try {
    const {
      status,
      search,
      category,
      lowStock,
      expiring,
      page = 1,
      limit = 20,
      sortBy = 'name'
    } = req.query;

    const query = { pharmacy: req.user.id, isActive: true };

    // Status filter
    if (status) query.status = status;

    // Low stock filter
    if (lowStock === 'true') {
      query.status = 'low_stock';
    }

    // Expiring filter
    if (expiring === 'true') {
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + 30);
      query['batch.expiryDate'] = { $lte: expiryDate, $gte: new Date() };
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    let inventoryQuery = Inventory.find(query)
      .populate('medicine', 'name manufacturer category form strength images')
      .skip(skip)
      .limit(parseInt(limit));

    // Sorting
    switch (sortBy) {
      case 'quantity':
        inventoryQuery = inventoryQuery.sort({ 'stock.currentQuantity': -1 });
        break;
      case 'expiry':
        inventoryQuery = inventoryQuery.sort({ 'batch.expiryDate': 1 });
        break;
      case 'price':
        inventoryQuery = inventoryQuery.sort({ 'pricing.sellingPrice': 1 });
        break;
      default:
        inventoryQuery = inventoryQuery.sort({ 'medicine.name': 1 });
    }

    let inventory = await inventoryQuery;

    // Search filter (applied after population)
    if (search) {
      inventory = inventory.filter(item =>
        item.medicine.name.toLowerCase().includes(search.toLowerCase()) ||
        item.medicine.manufacturer.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Category filter (applied after population)
    if (category) {
      inventory = inventory.filter(item => item.medicine.category === category);
    }

    const total = await Inventory.countDocuments(query);

    res.json({
      success: true,
      data: {
        inventory,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get inventory error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المخزون'
    });
  }
});

// @desc    Get inventory item by ID
// @route   GET /api/inventory/:id
// @access  Private/Pharmacy
router.get('/:id', protect, authorize('pharmacy'), async (req, res) => {
  try {
    const inventoryItem = await Inventory.findById(req.params.id)
      .populate('medicine')
      .populate('movements.user', 'name');

    if (!inventoryItem) {
      return res.status(404).json({
        success: false,
        message: 'عنصر المخزون غير موجود'
      });
    }

    // Check if pharmacy owns this inventory item
    if (inventoryItem.pharmacy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بعرض هذا العنصر'
      });
    }

    res.json({
      success: true,
      data: { inventoryItem }
    });
  } catch (error) {
    console.error('Get inventory item error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب عنصر المخزون'
    });
  }
});

// @desc    Add medicine to inventory
// @route   POST /api/inventory
// @access  Private/Pharmacy
router.post('/', protect, authorize('pharmacy'), [
  body('medicine').isMongoId().withMessage('معرف الدواء غير صحيح'),
  body('stock.currentQuantity').isInt({ min: 0 }).withMessage('الكمية الحالية يجب أن تكون رقم صحيح غير سالب'),
  body('stock.minimumQuantity').optional().isInt({ min: 0 }).withMessage('الحد الأدنى يجب أن يكون رقم صحيح غير سالب'),
  body('pricing.costPrice').isFloat({ min: 0 }).withMessage('سعر التكلفة يجب أن يكون رقم غير سالب'),
  body('pricing.sellingPrice').isFloat({ min: 0 }).withMessage('سعر البيع يجب أن يكون رقم غير سالب'),
  body('batch.expiryDate').isISO8601().withMessage('تاريخ انتهاء الصلاحية غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }

    const {
      medicine,
      stock,
      pricing,
      batch,
      location
    } = req.body;

    // Check if medicine exists
    const medicineDoc = await Medicine.findById(medicine);
    if (!medicineDoc || !medicineDoc.isActive || !medicineDoc.isApproved) {
      return res.status(400).json({
        success: false,
        message: 'الدواء غير متاح'
      });
    }

    // Check if medicine already exists in pharmacy inventory
    const existingInventory = await Inventory.findOne({
      pharmacy: req.user.id,
      medicine: medicine
    });

    if (existingInventory) {
      return res.status(400).json({
        success: false,
        message: 'هذا الدواء موجود بالفعل في المخزون'
      });
    }

    // Create inventory item
    const inventoryItem = await Inventory.create({
      pharmacy: req.user.id,
      medicine,
      stock,
      pricing,
      batch,
      location
    });

    // Add initial movement
    await inventoryItem.addMovement(
      'purchase',
      stock.currentQuantity,
      'إضافة أولية للمخزون',
      batch.batchNumber || 'INITIAL',
      req.user.id
    );

    await inventoryItem.populate('medicine', 'name manufacturer form strength');

    res.status(201).json({
      success: true,
      message: 'تم إضافة الدواء للمخزون بنجاح',
      data: { inventoryItem }
    });
  } catch (error) {
    console.error('Add inventory error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إضافة الدواء للمخزون'
    });
  }
});

// @desc    Update inventory item
// @route   PUT /api/inventory/:id
// @access  Private/Pharmacy
router.put('/:id', protect, authorize('pharmacy'), async (req, res) => {
  try {
    const inventoryItem = await Inventory.findById(req.params.id);

    if (!inventoryItem) {
      return res.status(404).json({
        success: false,
        message: 'عنصر المخزون غير موجود'
      });
    }

    // Check if pharmacy owns this inventory item
    if (inventoryItem.pharmacy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بتحديث هذا العنصر'
      });
    }

    const {
      stock,
      pricing,
      batch,
      location,
      adjustmentReason
    } = req.body;

    // Handle quantity adjustment
    if (stock && stock.currentQuantity !== undefined && stock.currentQuantity !== inventoryItem.stock.currentQuantity) {
      await inventoryItem.addMovement(
        'adjustment',
        stock.currentQuantity,
        adjustmentReason || 'تعديل الكمية',
        'ADJ-' + Date.now(),
        req.user.id
      );
    } else {
      // Update other fields
      if (stock) Object.assign(inventoryItem.stock, stock);
      if (pricing) Object.assign(inventoryItem.pricing, pricing);
      if (batch) Object.assign(inventoryItem.batch, batch);
      if (location) Object.assign(inventoryItem.location, location);

      await inventoryItem.save();
    }

    await inventoryItem.populate('medicine', 'name manufacturer form strength');

    res.json({
      success: true,
      message: 'تم تحديث عنصر المخزون بنجاح',
      data: { inventoryItem }
    });
  } catch (error) {
    console.error('Update inventory error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث عنصر المخزون'
    });
  }
});

// @desc    Delete inventory item
// @route   DELETE /api/inventory/:id
// @access  Private/Pharmacy
router.delete('/:id', protect, authorize('pharmacy'), async (req, res) => {
  try {
    const inventoryItem = await Inventory.findById(req.params.id);

    if (!inventoryItem) {
      return res.status(404).json({
        success: false,
        message: 'عنصر المخزون غير موجود'
      });
    }

    // Check if pharmacy owns this inventory item
    if (inventoryItem.pharmacy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بحذف هذا العنصر'
      });
    }

    // Soft delete - deactivate instead of removing
    inventoryItem.isActive = false;
    await inventoryItem.save();

    res.json({
      success: true,
      message: 'تم حذف عنصر المخزون بنجاح'
    });
  } catch (error) {
    console.error('Delete inventory error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف عنصر المخزون'
    });
  }
});

// @desc    Get inventory statistics
// @route   GET /api/inventory/stats/overview
// @access  Private/Pharmacy
router.get('/stats/overview', protect, authorize('pharmacy'), async (req, res) => {
  try {
    const stats = await Inventory.getInventoryStats(req.user.id);
    const lowStockItems = await Inventory.getLowStockItems(req.user.id);
    const expiringItems = await Inventory.getExpiringItems(req.user.id, 30);
    const topSellingItems = await Inventory.getTopSellingItems(req.user.id, 10);

    res.json({
      success: true,
      data: {
        statistics: stats[0] || {},
        alerts: {
          lowStock: lowStockItems,
          expiring: expiringItems
        },
        topSelling: topSellingItems
      }
    });
  } catch (error) {
    console.error('Get inventory stats error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب إحصائيات المخزون'
    });
  }
});

// @desc    Get movement history
// @route   GET /api/inventory/movements
// @access  Private/Pharmacy
router.get('/movements', protect, authorize('pharmacy'), async (req, res) => {
  try {
    const { startDate, endDate, type, page = 1, limit = 50 } = req.query;

    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Default: last 30 days
    const end = endDate ? new Date(endDate) : new Date();

    const movements = await Inventory.getMovementHistory(req.user.id, start, end);

    // Filter by type if specified
    let filteredMovements = movements;
    if (type) {
      filteredMovements = movements.filter(m => m.movements.type === type);
    }

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const paginatedMovements = filteredMovements.slice(skip, skip + parseInt(limit));

    res.json({
      success: true,
      data: {
        movements: paginatedMovements,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: filteredMovements.length,
          pages: Math.ceil(filteredMovements.length / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get movement history error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب تاريخ الحركات'
    });
  }
});

module.exports = router;
