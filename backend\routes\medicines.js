const express = require('express');
const Medicine = require('../models/Medicine');
const Inventory = require('../models/Inventory');
const { protect, authorize } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

const router = express.Router();

// @desc    Get all medicines with search and filters
// @route   GET /api/medicines
// @access  Public
router.get('/', async (req, res) => {
  try {
    const {
      q: query,
      category,
      form,
      prescriptionRequired,
      inStock,
      minPrice,
      maxPrice,
      sortBy = 'relevance',
      page = 1,
      limit = 20,
      pharmacyId
    } = req.query;

    const options = {
      category,
      form,
      prescriptionRequired: prescriptionRequired === 'true' ? true : prescriptionRequired === 'false' ? false : undefined,
      inStock: inStock === 'true' ? true : inStock === 'false' ? false : undefined,
      minPrice: minPrice ? parseFloat(minPrice) : undefined,
      maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,
      sortBy,
      page: parseInt(page),
      limit: parseInt(limit)
    };

    let medicines = await Medicine.searchMedicines(query, options);

    // If pharmacy ID is provided, get inventory information
    if (pharmacyId) {
      const medicineIds = medicines.map(med => med._id);
      const inventoryItems = await Inventory.find({
        pharmacy: pharmacyId,
        medicine: { $in: medicineIds },
        isActive: true
      });

      // Add inventory info to medicines
      medicines = medicines.map(medicine => {
        const inventory = inventoryItems.find(inv => 
          inv.medicine.toString() === medicine._id.toString()
        );
        
        return {
          ...medicine.toObject(),
          inventory: inventory ? {
            available: inventory.availableQuantity > 0,
            quantity: inventory.availableQuantity,
            price: inventory.pricing.sellingPrice,
            discountPrice: inventory.pricing.discountPrice
          } : null
        };
      });
    }

    const total = await Medicine.countDocuments({
      isActive: true,
      isApproved: true,
      ...(query && { $text: { $search: query } }),
      ...(category && { category }),
      ...(form && { form }),
      ...(prescriptionRequired !== undefined && { prescriptionRequired }),
      ...(options.minPrice && { 'pricing.finalPrice': { $gte: options.minPrice } }),
      ...(options.maxPrice && { 'pricing.finalPrice': { $lte: options.maxPrice } })
    });

    res.json({
      success: true,
      data: {
        medicines,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get medicines error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الأدوية'
    });
  }
});

// @desc    Get medicine by ID
// @route   GET /api/medicines/:id
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const medicine = await Medicine.findById(req.params.id)
      .populate('approvedBy', 'name');

    if (!medicine) {
      return res.status(404).json({
        success: false,
        message: 'الدواء غير موجود'
      });
    }

    if (!medicine.isActive || !medicine.isApproved) {
      return res.status(404).json({
        success: false,
        message: 'الدواء غير متاح حالياً'
      });
    }

    // Increment view count
    await medicine.incrementViewCount();

    // Get availability from nearby pharmacies if location provided
    const { lat, lng, radius = 10 } = req.query;
    let availability = [];

    if (lat && lng) {
      const nearbyPharmacies = await require('../models/Pharmacy').findNearby(
        parseFloat(lng), 
        parseFloat(lat), 
        parseFloat(radius)
      );

      const pharmacyIds = nearbyPharmacies.map(p => p._id);
      
      availability = await Inventory.find({
        medicine: medicine._id,
        pharmacy: { $in: pharmacyIds },
        'stock.currentQuantity': { $gt: 0 },
        isActive: true
      })
      .populate('pharmacy', 'name address phone services.delivery')
      .select('pharmacy pricing.sellingPrice pricing.discountPrice stock.currentQuantity');
    }

    res.json({
      success: true,
      data: {
        medicine,
        availability
      }
    });
  } catch (error) {
    console.error('Get medicine error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الدواء'
    });
  }
});

// @desc    Get medicine categories
// @route   GET /api/medicines/categories/list
// @access  Public
router.get('/categories/list', async (req, res) => {
  try {
    const categories = await Medicine.getCategoryStats();
    
    res.json({
      success: true,
      data: { categories }
    });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الفئات'
    });
  }
});

// @desc    Add new medicine (Admin only)
// @route   POST /api/medicines
// @access  Private/Admin
router.post('/', protect, authorize('admin'), [
  body('name').trim().isLength({ min: 2, max: 200 }).withMessage('اسم الدواء يجب أن يكون بين 2 و 200 حرف'),
  body('manufacturer').trim().notEmpty().withMessage('الشركة المصنعة مطلوبة'),
  body('category').notEmpty().withMessage('فئة الدواء مطلوبة'),
  body('form').notEmpty().withMessage('شكل الدواء مطلوب'),
  body('strength').notEmpty().withMessage('تركيز الدواء مطلوب'),
  body('packSize').notEmpty().withMessage('حجم العبوة مطلوب'),
  body('pricing.basePrice').isFloat({ min: 0 }).withMessage('السعر الأساسي مطلوب ولا يمكن أن يكون سالب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }

    const medicineData = {
      ...req.body,
      isApproved: true,
      approvedBy: req.user.id,
      approvedAt: new Date()
    };

    const medicine = await Medicine.create(medicineData);

    res.status(201).json({
      success: true,
      message: 'تم إضافة الدواء بنجاح',
      data: { medicine }
    });
  } catch (error) {
    console.error('Add medicine error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إضافة الدواء'
    });
  }
});

// @desc    Update medicine (Admin only)
// @route   PUT /api/medicines/:id
// @access  Private/Admin
router.put('/:id', protect, authorize('admin'), async (req, res) => {
  try {
    const medicine = await Medicine.findById(req.params.id);

    if (!medicine) {
      return res.status(404).json({
        success: false,
        message: 'الدواء غير موجود'
      });
    }

    // Update medicine
    Object.assign(medicine, req.body);
    await medicine.save();

    res.json({
      success: true,
      message: 'تم تحديث الدواء بنجاح',
      data: { medicine }
    });
  } catch (error) {
    console.error('Update medicine error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث الدواء'
    });
  }
});

// @desc    Delete medicine (Admin only)
// @route   DELETE /api/medicines/:id
// @access  Private/Admin
router.delete('/:id', protect, authorize('admin'), async (req, res) => {
  try {
    const medicine = await Medicine.findById(req.params.id);

    if (!medicine) {
      return res.status(404).json({
        success: false,
        message: 'الدواء غير موجود'
      });
    }

    // Soft delete - deactivate instead of removing
    medicine.isActive = false;
    await medicine.save();

    res.json({
      success: true,
      message: 'تم حذف الدواء بنجاح'
    });
  } catch (error) {
    console.error('Delete medicine error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف الدواء'
    });
  }
});

// @desc    Get medicine statistics (Admin only)
// @route   GET /api/medicines/stats/overview
// @access  Private/Admin
router.get('/stats/overview', protect, authorize('admin'), async (req, res) => {
  try {
    const stats = await Medicine.getStatistics();
    const categoryStats = await Medicine.getCategoryStats();

    res.json({
      success: true,
      data: {
        overview: stats[0] || {},
        categories: categoryStats
      }
    });
  } catch (error) {
    console.error('Get medicine stats error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب إحصائيات الأدوية'
    });
  }
});

module.exports = router;
