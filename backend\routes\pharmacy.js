const express = require('express');
const Pharmacy = require('../models/Pharmacy');
const Medicine = require('../models/Medicine');
const Inventory = require('../models/Inventory');
const Order = require('../models/Order');
const { protect, requireVerification, authorize, generateToken, generateRefreshToken } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

const router = express.Router();

// @desc    Register pharmacy
// @route   POST /api/pharmacy/register
// @access  Public
router.post('/register', [
  body('name').trim().isLength({ min: 2, max: 100 }).withMessage('اسم الصيدلية يجب أن يكون بين 2 و 100 حرف'),
  body('email').isEmail().withMessage('يرجى إدخال بريد إلكتروني صحيح').normalizeEmail(),
  body('phone').matches(/^[0-9]{10,15}$/).withMessage('رقم الهاتف يجب أن يكون بين 10 و 15 رقم'),
  body('password').isLength({ min: 6 }).withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  body('licenseNumber').notEmpty().withMessage('رقم الترخيص مطلوب'),
  body('address.street').notEmpty().withMessage('عنوان الشارع مطلوب'),
  body('address.city').notEmpty().withMessage('المدينة مطلوبة'),
  body('address.governorate').notEmpty().withMessage('المحافظة مطلوبة'),
  body('location.coordinates').isArray({ min: 2, max: 2 }).withMessage('الإحداثيات مطلوبة')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }

    const {
      name, email, phone, password, licenseNumber, address, location,
      workingHours, services, documents, bankAccount
    } = req.body;

    // Check if pharmacy already exists
    const existingPharmacy = await Pharmacy.findOne({
      $or: [{ email }, { licenseNumber }, { phone }]
    });

    if (existingPharmacy) {
      let message = 'البيانات مستخدمة بالفعل';
      if (existingPharmacy.email === email) message = 'البريد الإلكتروني مستخدم بالفعل';
      if (existingPharmacy.licenseNumber === licenseNumber) message = 'رقم الترخيص مستخدم بالفعل';
      if (existingPharmacy.phone === phone) message = 'رقم الهاتف مستخدم بالفعل';

      return res.status(400).json({
        success: false,
        message
      });
    }

    // Create pharmacy
    const pharmacy = await Pharmacy.create({
      name, email, phone, password, licenseNumber, address, location,
      workingHours, services, documents, bankAccount
    });

    // Generate tokens
    const accessToken = generateToken({ id: pharmacy._id, type: 'pharmacy' });
    const refreshTokenValue = generateRefreshToken({ id: pharmacy._id, type: 'pharmacy' });

    // Save refresh token
    pharmacy.refreshTokens.push({ token: refreshTokenValue });
    await pharmacy.save();

    // Remove password from response
    pharmacy.password = undefined;

    res.status(201).json({
      success: true,
      message: 'تم تسجيل الصيدلية بنجاح. في انتظار المراجعة والتفعيل',
      data: {
        pharmacy,
        accessToken,
        refreshToken: refreshTokenValue
      }
    });
  } catch (error) {
    console.error('Pharmacy registration error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تسجيل الصيدلية'
    });
  }
});

// @desc    Login pharmacy
// @route   POST /api/pharmacy/login
// @access  Public
router.post('/login', [
  body('email').isEmail().withMessage('يرجى إدخال بريد إلكتروني صحيح').normalizeEmail(),
  body('password').notEmpty().withMessage('كلمة المرور مطلوبة')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }

    const { email, password } = req.body;

    // Find pharmacy and include password
    const pharmacy = await Pharmacy.findOne({ email }).select('+password');

    if (!pharmacy) {
      return res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة'
      });
    }

    // Check if account is locked
    if (pharmacy.isLocked) {
      const lockTimeRemaining = Math.ceil((pharmacy.lockUntil - Date.now()) / 1000 / 60);
      return res.status(423).json({
        success: false,
        message: `تم قفل الحساب مؤقتاً. يرجى المحاولة بعد ${lockTimeRemaining} دقيقة`,
        lockTimeRemaining
      });
    }

    // Check if account is active
    if (!pharmacy.isActive) {
      return res.status(401).json({
        success: false,
        message: 'تم إلغاء تفعيل الحساب'
      });
    }

    // Check password
    const isPasswordCorrect = await pharmacy.comparePassword(password);

    if (!isPasswordCorrect) {
      await pharmacy.incLoginAttempts();
      return res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة'
      });
    }

    // Reset login attempts on successful login
    if (pharmacy.loginAttempts > 0) {
      await pharmacy.resetLoginAttempts();
    }

    // Update last login
    pharmacy.lastLogin = new Date();
    await pharmacy.save();

    // Generate tokens
    const accessToken = generateToken({ id: pharmacy._id, type: 'pharmacy' });
    const refreshTokenValue = generateRefreshToken({ id: pharmacy._id, type: 'pharmacy' });

    // Save refresh token
    pharmacy.refreshTokens.push({ token: refreshTokenValue });
    await pharmacy.save();

    // Remove password from response
    pharmacy.password = undefined;

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      data: {
        pharmacy,
        accessToken,
        refreshToken: refreshTokenValue
      }
    });
  } catch (error) {
    console.error('Pharmacy login error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تسجيل الدخول'
    });
  }
});

// @desc    Get all pharmacies (with location filtering)
// @route   GET /api/pharmacy
// @access  Public
router.get('/', async (req, res) => {
  try {
    const {
      lat, lng, radius = 10, // Default 10km radius
      governorate, city,
      services, rating,
      page = 1, limit = 20,
      sortBy = 'distance'
    } = req.query;

    let query = { isActive: true, isVerified: true };
    let pharmacies;

    // Location-based search
    if (lat && lng) {
      pharmacies = await Pharmacy.findNearby(
        parseFloat(lng),
        parseFloat(lat),
        parseFloat(radius)
      );
    } else {
      // Regular search
      if (governorate) query['address.governorate'] = governorate;
      if (city) query['address.city'] = city;
      if (rating) query['rating.average'] = { $gte: parseFloat(rating) };

      // Services filter
      if (services) {
        const servicesList = services.split(',');
        servicesList.forEach(service => {
          query[`services.${service}.available`] = true;
        });
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);

      let sort = {};
      switch (sortBy) {
        case 'rating':
          sort = { 'rating.average': -1 };
          break;
        case 'name':
          sort = { name: 1 };
          break;
        default:
          sort = { createdAt: -1 };
      }

      pharmacies = await Pharmacy.find(query)
        .select('-password -refreshTokens -documents -bankAccount')
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit));
    }

    const total = await Pharmacy.countDocuments(query);

    res.json({
      success: true,
      message: 'قائمة الصيدليات',
      data: {
        pharmacies,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get pharmacies error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الصيدليات'
    });
  }
});

// @desc    Get pharmacy by ID
// @route   GET /api/pharmacy/:id
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const pharmacy = await Pharmacy.findById(req.params.id)
      .select('-password -refreshTokens -documents -bankAccount');

    if (!pharmacy) {
      return res.status(404).json({
        success: false,
        message: 'الصيدلية غير موجودة'
      });
    }

    if (!pharmacy.isActive || !pharmacy.isVerified) {
      return res.status(404).json({
        success: false,
        message: 'الصيدلية غير متاحة حالياً'
      });
    }

    res.json({
      success: true,
      message: 'تفاصيل الصيدلية',
      data: { pharmacy }
    });
  } catch (error) {
    console.error('Get pharmacy error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الصيدلية'
    });
  }
});

// @desc    Get pharmacy dashboard data
// @route   GET /api/pharmacy/dashboard
// @access  Private (Pharmacy)
router.get('/dashboard', protect, authorize('pharmacy'), async (req, res) => {
  try {
    const pharmacyId = req.user.id;

    // Get statistics
    const [orderStats, inventoryStats] = await Promise.all([
      Order.getStatistics(pharmacyId),
      Inventory.getInventoryStats(pharmacyId)
    ]);

    // Get recent orders
    const recentOrders = await Order.find({ pharmacy: pharmacyId })
      .populate('user', 'name phone')
      .sort({ createdAt: -1 })
      .limit(10);

    // Get low stock items
    const lowStockItems = await Inventory.getLowStockItems(pharmacyId);

    // Get expiring items
    const expiringItems = await Inventory.getExpiringItems(pharmacyId, 30);

    res.json({
      success: true,
      data: {
        statistics: {
          orders: orderStats[0] || {},
          inventory: inventoryStats[0] || {}
        },
        recentOrders,
        alerts: {
          lowStock: lowStockItems,
          expiring: expiringItems
        }
      }
    });
  } catch (error) {
    console.error('Get dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب بيانات لوحة التحكم'
    });
  }
});

module.exports = router;
