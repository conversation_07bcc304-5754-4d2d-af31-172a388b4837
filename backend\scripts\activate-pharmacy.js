const mongoose = require('mongoose');
const Pharmacy = require('../models/Pharmacy');
require('dotenv').config();

// اتصال بقاعدة البيانات
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/pharmacy-db');

async function activatePharmacy(email) {
  try {
    console.log(`🔍 البحث عن الصيدلية: ${email}`);
    
    const pharmacy = await Pharmacy.findOne({ email });
    
    if (!pharmacy) {
      console.log('❌ الصيدلية غير موجودة');
      return;
    }
    
    console.log(`📋 الصيدلية الموجودة: ${pharmacy.name}`);
    console.log(`📊 الحالة الحالية: isActive=${pharmacy.isActive}, isVerified=${pharmacy.isVerified}`);
    
    // تفعيل الصيدلية
    pharmacy.isVerified = true;
    pharmacy.isActive = true;
    await pharmacy.save();
    
    console.log('✅ تم تفعيل الصيدلية بنجاح!');
    console.log(`📊 الحالة الجديدة: isActive=${pharmacy.isActive}, isVerified=${pharmacy.isVerified}`);
    
  } catch (error) {
    console.error('❌ خطأ في تفعيل الصيدلية:', error);
  } finally {
    mongoose.connection.close();
  }
}

async function listPharmacies() {
  try {
    console.log('📋 قائمة جميع الصيدليات:');
    console.log('='.repeat(50));
    
    const pharmacies = await Pharmacy.find({})
      .select('name email isActive isVerified createdAt')
      .sort({ createdAt: -1 });
    
    if (pharmacies.length === 0) {
      console.log('📭 لا توجد صيدليات مسجلة');
      return;
    }
    
    pharmacies.forEach((pharmacy, index) => {
      const status = pharmacy.isVerified ? '✅ مفعلة' : '⏳ في انتظار التفعيل';
      const active = pharmacy.isActive ? '🟢 نشطة' : '🔴 معطلة';
      
      console.log(`${index + 1}. ${pharmacy.name}`);
      console.log(`   📧 الإيميل: ${pharmacy.email}`);
      console.log(`   📊 الحالة: ${status} | ${active}`);
      console.log(`   📅 تاريخ التسجيل: ${pharmacy.createdAt.toLocaleDateString('ar-EG')}`);
      console.log('-'.repeat(30));
    });
    
  } catch (error) {
    console.error('❌ خطأ في جلب الصيدليات:', error);
  } finally {
    mongoose.connection.close();
  }
}

async function deactivatePharmacy(email) {
  try {
    console.log(`🔍 البحث عن الصيدلية: ${email}`);
    
    const pharmacy = await Pharmacy.findOne({ email });
    
    if (!pharmacy) {
      console.log('❌ الصيدلية غير موجودة');
      return;
    }
    
    console.log(`📋 الصيدلية الموجودة: ${pharmacy.name}`);
    
    // إلغاء تفعيل الصيدلية
    pharmacy.isVerified = false;
    pharmacy.isActive = false;
    await pharmacy.save();
    
    console.log('🔴 تم إلغاء تفعيل الصيدلية');
    
  } catch (error) {
    console.error('❌ خطأ في إلغاء تفعيل الصيدلية:', error);
  } finally {
    mongoose.connection.close();
  }
}

// معالجة الأوامر من سطر الأوامر
const command = process.argv[2];
const email = process.argv[3];

switch (command) {
  case 'activate':
    if (!email) {
      console.log('❌ يرجى إدخال إيميل الصيدلية');
      console.log('مثال: node activate-pharmacy.<NAME_EMAIL>');
      process.exit(1);
    }
    activatePharmacy(email);
    break;
    
  case 'deactivate':
    if (!email) {
      console.log('❌ يرجى إدخال إيميل الصيدلية');
      console.log('مثال: node activate-pharmacy.<NAME_EMAIL>');
      process.exit(1);
    }
    deactivatePharmacy(email);
    break;
    
  case 'list':
    listPharmacies();
    break;
    
  default:
    console.log('🔧 أداة إدارة الصيدليات');
    console.log('='.repeat(30));
    console.log('الأوامر المتاحة:');
    console.log('  list                           - عرض جميع الصيدليات');
    console.log('  activate <email>              - تفعيل صيدلية');
    console.log('  deactivate <email>            - إلغاء تفعيل صيدلية');
    console.log('');
    console.log('أمثلة:');
    console.log('  node activate-pharmacy.js list');
    console.log('  node activate-pharmacy.<NAME_EMAIL>');
    console.log('  node activate-pharmacy.<NAME_EMAIL>');
    break;
}
