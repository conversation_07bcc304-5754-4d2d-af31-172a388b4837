const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/pharmacy_app', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

mongoose.connection.on('connected', () => {
  console.log('✅ متصل بقاعدة البيانات');
});

mongoose.connection.on('error', (err) => {
  console.error('❌ خطأ في الاتصال بقاعدة البيانات:', err);
});

const Order = require('../models/Order');

async function fixHasImageData() {
  try {
    console.log('🔧 بدء إصلاح بيانات hasImage...');

    // انتظار الاتصال
    await new Promise((resolve) => {
      if (mongoose.connection.readyState === 1) {
        resolve();
      } else {
        mongoose.connection.once('connected', resolve);
      }
    });
    
    // البحث عن جميع الطلبات التي تحتوي على prescription.hasImage من نوع String
    const orders = await Order.find({
      'prescription.hasImage': { $type: 'string' }
    });
    
    console.log(`📋 تم العثور على ${orders.length} طلب يحتاج إصلاح`);
    
    let fixedCount = 0;
    
    for (const order of orders) {
      try {
        const originalValue = order.prescription.hasImage;
        
        // تحويل String إلى Boolean
        if (typeof originalValue === 'string') {
          order.prescription.hasImage = originalValue === 'true' || 
                                       originalValue === '1' ||
                                       originalValue.toLowerCase() === 'yes';
          
          await order.save();
          fixedCount++;
          
          console.log(`✅ تم إصلاح الطلب ${order.orderNumber}: "${originalValue}" → ${order.prescription.hasImage}`);
        }
      } catch (error) {
        console.error(`❌ خطأ في إصلاح الطلب ${order.orderNumber}:`, error.message);
      }
    }
    
    console.log(`🎉 تم إصلاح ${fixedCount} طلب بنجاح`);
    
    // التحقق من النتائج
    const remainingStringValues = await Order.countDocuments({
      'prescription.hasImage': { $type: 'string' }
    });
    
    console.log(`📊 عدد الطلبات المتبقية بقيم String: ${remainingStringValues}`);
    
    if (remainingStringValues === 0) {
      console.log('✅ تم إصلاح جميع البيانات بنجاح!');
    } else {
      console.log('⚠️ ما زالت هناك بعض البيانات تحتاج إصلاح');
    }
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح البيانات:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 تم إغلاق الاتصال بقاعدة البيانات');
  }
}

// تشغيل الإصلاح
fixHasImageData();
