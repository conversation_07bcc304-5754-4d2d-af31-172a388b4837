const mongoose = require('mongoose');
const User = require('../models/User');
const Pharmacy = require('../models/Pharmacy');
const Medicine = require('../models/Medicine');
const Inventory = require('../models/Inventory');
require('dotenv').config();

async function setupDatabase() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB successfully');

    // Create indexes
    console.log('🔄 Creating database indexes...');
    
    await User.createIndexes();
    console.log('✅ User indexes created');

    // Create admin user if it doesn't exist
    console.log('🔄 Checking for admin user...');
    
    const adminExists = await User.findOne({ role: 'admin' });
    
    if (!adminExists) {
      const adminUser = await User.create({
        name: 'مدير النظام',
        email: '<EMAIL>',
        phone: '01000000000',
        password: 'admin123456',
        address: 'القاهرة، مصر',
        governorate: 'القاهرة',
        role: 'admin',
        isEmailVerified: true,
        isActive: true
      });
      
      console.log('✅ Admin user created successfully');
      console.log('📧 Admin email: <EMAIL>');
      console.log('🔑 Admin password: admin123456');
    } else {
      console.log('ℹ️  Admin user already exists');
    }

    // Database statistics
    console.log('\n📊 Database Statistics:');
    const userCount = await User.countDocuments();
    const activeUsers = await User.countDocuments({ isActive: true });
    const verifiedUsers = await User.countDocuments({ isEmailVerified: true });
    
    console.log(`👥 Total users: ${userCount}`);
    console.log(`✅ Active users: ${activeUsers}`);
    console.log(`📧 Verified users: ${verifiedUsers}`);

    // Create sample medicines
    console.log('🔄 Creating sample medicines...');
    const medicines = [
      {
        name: 'باراسيتامول 500 مجم',
        genericName: 'Paracetamol',
        manufacturer: 'شركة الإسكندرية للأدوية',
        category: 'مسكنات',
        form: 'أقراص',
        strength: '500 مجم',
        packSize: '20 قرص',
        description: 'مسكن للألم وخافض للحرارة',
        pricing: { basePrice: 15.50 },
        prescriptionRequired: false,
        isActive: true,
        isApproved: true,
        approvedBy: adminUser._id,
        approvedAt: new Date()
      },
      {
        name: 'أموكسيسيلين 500 مجم',
        genericName: 'Amoxicillin',
        manufacturer: 'شركة القاهرة للأدوية',
        category: 'مضادات حيوية',
        form: 'كبسولات',
        strength: '500 مجم',
        packSize: '12 كبسولة',
        description: 'مضاد حيوي واسع المجال',
        pricing: { basePrice: 45.00 },
        prescriptionRequired: true,
        isActive: true,
        isApproved: true,
        approvedBy: adminUser._id,
        approvedAt: new Date()
      },
      {
        name: 'فيتامين د 1000 وحدة',
        genericName: 'Vitamin D3',
        manufacturer: 'شركة الدلتا للأدوية',
        category: 'فيتامينات ومكملات',
        form: 'أقراص',
        strength: '1000 وحدة دولية',
        packSize: '30 قرص',
        description: 'مكمل غذائي لفيتامين د',
        pricing: { basePrice: 35.00 },
        prescriptionRequired: false,
        isActive: true,
        isApproved: true,
        approvedBy: adminUser._id,
        approvedAt: new Date()
      }
    ];

    const createdMedicines = await Medicine.insertMany(medicines);
    console.log(`✅ Created ${createdMedicines.length} sample medicines`);

    // Create sample pharmacy
    console.log('🔄 Creating sample pharmacy...');
    const samplePharmacy = await Pharmacy.create({
      name: 'صيدلية النور',
      email: '<EMAIL>',
      phone: '01234567890',
      password: 'pharmacy123',
      licenseNumber: 'PH-2024-001',
      address: {
        street: 'شارع النيل الرئيسي',
        city: 'القاهرة',
        governorate: 'القاهرة',
        postalCode: '11511'
      },
      location: {
        type: 'Point',
        coordinates: [31.2357, 30.0444] // Cairo coordinates
      },
      workingHours: {
        monday: { open: '08:00', close: '22:00', isOpen: true },
        tuesday: { open: '08:00', close: '22:00', isOpen: true },
        wednesday: { open: '08:00', close: '22:00', isOpen: true },
        thursday: { open: '08:00', close: '22:00', isOpen: true },
        friday: { open: '08:00', close: '22:00', isOpen: true },
        saturday: { open: '08:00', close: '22:00', isOpen: true },
        sunday: { open: '10:00', close: '20:00', isOpen: true }
      },
      services: {
        delivery: {
          available: true,
          fee: 15,
          freeDeliveryMinimum: 100,
          estimatedTime: 30
        },
        consultation: true,
        insurance: true,
        emergencyService: false
      },
      isVerified: true,
      isActive: true
    });

    console.log('✅ Sample pharmacy created');

    // Create inventory for the pharmacy
    console.log('🔄 Creating sample inventory...');
    const inventoryItems = createdMedicines.map(medicine => ({
      pharmacy: samplePharmacy._id,
      medicine: medicine._id,
      stock: {
        currentQuantity: Math.floor(Math.random() * 100) + 20,
        minimumQuantity: 10,
        maximumQuantity: 200,
        reservedQuantity: 0
      },
      pricing: {
        costPrice: medicine.pricing.basePrice * 0.7,
        sellingPrice: medicine.pricing.basePrice,
        discountPrice: 0
      },
      batch: {
        batchNumber: `BATCH-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        manufacturingDate: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000), // 6 months ago
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        supplier: 'موزع الأدوية المصرية'
      },
      location: {
        shelf: `A${Math.floor(Math.random() * 10) + 1}`,
        section: `S${Math.floor(Math.random() * 5) + 1}`,
        position: `P${Math.floor(Math.random() * 20) + 1}`
      },
      isActive: true
    }));

    await Inventory.insertMany(inventoryItems);
    console.log(`✅ Created ${inventoryItems.length} inventory items`);

    console.log('\n🎉 Database setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupDatabase();
}

module.exports = setupDatabase;
