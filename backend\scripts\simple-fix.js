console.log('Starting script...');

const mongoose = require('mongoose');

console.log('Connecting to MongoDB...');
mongoose.connect('mongodb://localhost:27017/pharmacy_app')
  .then(() => {
    console.log('Connected to MongoDB');
    return fixData();
  })
  .catch(err => {
    console.error('Connection error:', err);
    process.exit(1);
  });

async function fixData() {
  try {
    console.log('Loading Order model...');
    const Order = require('../models/Order');
    
    console.log('Searching for orders with string hasImage...');
    const orders = await Order.find({
      'prescription.hasImage': { $exists: true }
    });
    
    console.log(`Found ${orders.length} orders with prescription.hasImage`);
    
    for (const order of orders) {
      console.log(`Order ${order.orderNumber}: hasImage = ${order.prescription.hasImage} (type: ${typeof order.prescription.hasImage})`);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Connection closed');
  }
}
