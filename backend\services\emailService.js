const nodemailer = require('nodemailer');

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Verify connection configuration
    this.transporter.verify((error, success) => {
      if (error) {
        console.error('❌ Email service configuration error:', error);
      } else {
        console.log('✅ Email service is ready to send messages');
      }
    });
  }

  async sendEmail(options) {
    try {
      const mailOptions = {
        from: `"صيدلية أونلاين" <${process.env.EMAIL_FROM}>`,
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text
      };

      const info = await this.transporter.sendMail(mailOptions);
      console.log('✅ Email sent successfully:', info.messageId);
      return info;
    } catch (error) {
      console.error('❌ Email sending failed:', error);
      throw new Error('فشل في إرسال البريد الإلكتروني');
    }
  }

  async sendVerificationEmail(user, token) {
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;
    
    const html = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تأكيد البريد الإلكتروني</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
          }
          .header {
            background: linear-gradient(135deg, #00BF63, #00A855);
            color: white;
            padding: 30px;
            text-align: center;
          }
          .header h1 {
            margin: 0;
            font-size: 28px;
          }
          .content {
            padding: 40px 30px;
          }
          .welcome {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2c3e50;
          }
          .message {
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.8;
          }
          .button {
            display: inline-block;
            background: #00BF63;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            font-size: 16px;
            margin: 20px 0;
            transition: background 0.3s;
          }
          .button:hover {
            background: #00A855;
          }
          .footer {
            background: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            font-size: 14px;
            color: #666;
            border-top: 1px solid #eee;
          }
          .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🏥 صيدلية أونلاين</h1>
            <p>مرحباً بك في منصة الصيدلية الإلكترونية</p>
          </div>
          
          <div class="content">
            <div class="welcome">
              مرحباً ${user.name}! 👋
            </div>
            
            <div class="message">
              شكراً لك على التسجيل في صيدلية أونلاين. لإكمال عملية التسجيل، يرجى تأكيد بريدك الإلكتروني بالنقر على الزر أدناه:
            </div>
            
            <div style="text-align: center;">
              <a href="${verificationUrl}" class="button">
                ✅ تأكيد البريد الإلكتروني
              </a>
            </div>
            
            <div class="warning">
              <strong>⚠️ تنبيه:</strong> هذا الرابط صالح لمدة 24 ساعة فقط. إذا لم تقم بالنقر عليه خلال هذه المدة، ستحتاج إلى طلب رابط جديد.
            </div>
            
            <div class="message">
              إذا لم تقم بإنشاء هذا الحساب، يرجى تجاهل هذا البريد الإلكتروني.
            </div>
          </div>
          
          <div class="footer">
            <p>© 2024 صيدلية أونلاين. جميع الحقوق محفوظة.</p>
            <p>هذا بريد إلكتروني تلقائي، يرجى عدم الرد عليه.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      مرحباً ${user.name}!
      
      شكراً لك على التسجيل في صيدلية أونلاين.
      
      لتأكيد بريدك الإلكتروني، يرجى زيارة الرابط التالي:
      ${verificationUrl}
      
      هذا الرابط صالح لمدة 24 ساعة فقط.
      
      إذا لم تقم بإنشاء هذا الحساب، يرجى تجاهل هذا البريد الإلكتروني.
      
      صيدلية أونلاين
    `;

    return await this.sendEmail({
      to: user.email,
      subject: '✅ تأكيد البريد الإلكتروني - صيدلية أونلاين',
      html,
      text
    });
  }

  async sendVerificationCode(user, code) {
    const html = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>كود تأكيد البريد الإلكتروني</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
            direction: rtl;
          }
          .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          .logo {
            background: linear-gradient(135deg, #00BF63, #00A855);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            display: inline-block;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
          }
          .verification-code {
            background: linear-gradient(135deg, #00BF63, #00A855);
            color: white;
            font-size: 32px;
            font-weight: bold;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 30px 0;
            letter-spacing: 5px;
            font-family: 'Courier New', monospace;
          }
          .content {
            text-align: center;
            margin-bottom: 30px;
          }
          .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
          }
          .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🏥 صيدلية أونلاين</div>
            <h1 style="color: #00BF63; margin: 0;">كود تأكيد البريد الإلكتروني</h1>
          </div>

          <div class="content">
            <p style="font-size: 18px; margin-bottom: 20px;">
              مرحباً <strong>${user.name}</strong>،
            </p>

            <p style="font-size: 16px; margin-bottom: 30px;">
              لتأكيد بريدك الإلكتروني، يرجى إدخال الكود التالي في التطبيق:
            </p>

            <div class="verification-code">
              ${code}
            </div>

            <div class="warning">
              <strong>⚠️ تنبيه:</strong> هذا الكود صالح لمدة 10 دقائق فقط
            </div>

            <p style="font-size: 14px; color: #666; margin-top: 30px;">
              إذا لم تقم بإنشاء حساب في صيدلية أونلاين، يرجى تجاهل هذا البريد الإلكتروني.
            </p>
          </div>

          <div class="footer">
            <p>
              <strong>صيدلية أونلاين</strong><br>
              خدمة توصيل الأدوية إلى منزلك<br>
              📧 ${process.env.EMAIL_FROM} | 📱 01234567890
            </p>
            <p style="font-size: 12px; color: #999; margin-top: 15px;">
              هذا بريد إلكتروني تلقائي، يرجى عدم الرد عليه
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      مرحباً ${user.name}،

      كود تأكيد البريد الإلكتروني: ${code}

      هذا الكود صالح لمدة 10 دقائق فقط.

      صيدلية أونلاين
    `;

    return await this.sendEmail({
      to: user.email,
      subject: 'كود تأكيد البريد الإلكتروني - صيدلية أونلاين',
      html,
      text
    });
  }

  async sendPasswordResetEmail(user, token) {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}`;
    
    const html = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إعادة تعيين كلمة المرور</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
          }
          .header {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            text-align: center;
          }
          .header h1 {
            margin: 0;
            font-size: 28px;
          }
          .content {
            padding: 40px 30px;
          }
          .message {
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.8;
          }
          .button {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            font-size: 16px;
            margin: 20px 0;
          }
          .footer {
            background: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            font-size: 14px;
            color: #666;
            border-top: 1px solid #eee;
          }
          .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔒 إعادة تعيين كلمة المرور</h1>
          </div>
          
          <div class="content">
            <div class="message">
              مرحباً ${user.name}،
            </div>
            
            <div class="message">
              تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك. انقر على الزر أدناه لإعادة تعيين كلمة المرور:
            </div>
            
            <div style="text-align: center;">
              <a href="${resetUrl}" class="button">
                🔑 إعادة تعيين كلمة المرور
              </a>
            </div>
            
            <div class="warning">
              <strong>⚠️ تنبيه:</strong> هذا الرابط صالح لمدة 10 دقائق فقط لأسباب أمنية.
            </div>
            
            <div class="message">
              إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني. حسابك آمن.
            </div>
          </div>
          
          <div class="footer">
            <p>© 2024 صيدلية أونلاين. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return await this.sendEmail({
      to: user.email,
      subject: '🔒 إعادة تعيين كلمة المرور - صيدلية أونلاين',
      html,
      text: `مرحباً ${user.name}، لإعادة تعيين كلمة المرور، يرجى زيارة: ${resetUrl}`
    });
  }

  async sendWelcomeEmail(user) {
    const html = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>مرحباً بك في صيدلية أونلاين</title>
      </head>
      <body style="font-family: Arial, sans-serif; direction: rtl;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #00BF63;">🎉 مرحباً بك ${user.name}!</h1>
          <p>تم تأكيد حسابك بنجاح. يمكنك الآن الاستفادة من جميع خدمات صيدلية أونلاين.</p>
          <p>نتمنى لك تجربة ممتعة ومفيدة!</p>
        </div>
      </body>
      </html>
    `;

    return await this.sendEmail({
      to: user.email,
      subject: '🎉 مرحباً بك في صيدلية أونلاين',
      html,
      text: `مرحباً بك ${user.name}! تم تأكيد حسابك بنجاح.`
    });
  }
}

module.exports = new EmailService();
