const http = require('http');

// Test user registration
const testData = JSON.stringify({
  name: 'أحمد محمد',
  email: '<EMAIL>',
  phone: '01234567890',
  password: 'password123',
  address: 'شارع النيل، المعادي',
  governorate: 'القاهرة'
});

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/auth/register',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(testData)
  }
};

console.log('🔄 Testing user registration...');
console.log('URL:', `http://${options.hostname}:${options.port}${options.path}`);
console.log('Data:', testData);

const req = http.request(options, (res) => {
  console.log(`\n📊 Status Code: ${res.statusCode}`);
  console.log('📋 Headers:', res.headers);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('\n📄 Response Body:');
    try {
      const jsonData = JSON.parse(data);
      console.log(JSON.stringify(jsonData, null, 2));
    } catch (e) {
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (e) => {
  console.error('❌ Request error:', e.message);
});

req.write(testData);
req.end();
