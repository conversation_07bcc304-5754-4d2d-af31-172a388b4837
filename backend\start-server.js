#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Pharmacy Backend Server...\n');

// Check if we're in the backend directory
const currentDir = process.cwd();
const backendDir = path.join(__dirname);

if (currentDir !== backendDir) {
  console.log(`📁 Changing directory to: ${backendDir}`);
  process.chdir(backendDir);
}

// Install dependencies if node_modules doesn't exist
const fs = require('fs');
if (!fs.existsSync('node_modules')) {
  console.log('📦 Installing dependencies...');
  const install = spawn('npm', ['install'], { stdio: 'inherit' });
  
  install.on('close', (code) => {
    if (code === 0) {
      console.log('✅ Dependencies installed successfully');
      startServer();
    } else {
      console.error('❌ Failed to install dependencies');
      process.exit(1);
    }
  });
} else {
  startServer();
}

function startServer() {
  console.log('🔄 Starting server...\n');
  
  // Start the server
  const server = spawn('node', ['server.js'], { 
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'production' }
  });

  server.on('close', (code) => {
    console.log(`\n🛑 Server stopped with code ${code}`);
  });

  server.on('error', (error) => {
    console.error('❌ Server error:', error);
  });

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    server.kill('SIGINT');
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down server...');
    server.kill('SIGTERM');
  });
}
