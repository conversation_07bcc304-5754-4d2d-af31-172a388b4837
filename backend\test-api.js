// Quick test script to verify API endpoints
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testAPI() {
  console.log('🧪 Testing Pharmacy Backend API...\n');

  try {
    // Test health check
    console.log('1. Testing health check...');
    const healthResponse = await axios.get('http://localhost:3000/health');
    console.log('✅ Health check:', healthResponse.data.message);

    // Test user registration
    console.log('\n2. Testing user registration...');
    const userData = {
      name: 'أحمد محمد',
      email: `test${Date.now()}@example.com`,
      phone: '01234567890',
      password: 'password123',
      address: 'شارع النيل، المعادي، القاهرة',
      governorate: 'القاهرة'
    };

    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, userData);
    console.log('✅ Registration successful:', registerResponse.data.message);
    
    const { accessToken, user } = registerResponse.data.data;
    console.log('📧 User email:', user.email);
    console.log('🔑 Access token received');

    // Test login
    console.log('\n3. Testing user login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: userData.email,
      password: userData.password
    });
    console.log('✅ Login successful:', loginResponse.data.message);

    // Test protected route
    console.log('\n4. Testing protected route...');
    const profileResponse = await axios.get(`${BASE_URL}/auth/me`, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });
    console.log('✅ Protected route access:', profileResponse.data.data.user.name);

    // Test email verification token generation
    console.log('\n5. Testing resend verification...');
    const resendResponse = await axios.post(`${BASE_URL}/auth/resend-verification`, {
      email: userData.email
    });
    console.log('✅ Verification email resent:', resendResponse.data.message);

    console.log('\n🎉 All tests passed successfully!');
    console.log('\n📝 Next steps:');
    console.log('- Set up your email credentials in .env file');
    console.log('- Test email verification with real email');
    console.log('- Implement frontend integration');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
    
    if (error.response?.data) {
      console.error('Error details:', error.response.data);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testAPI();
}

module.exports = testAPI;
