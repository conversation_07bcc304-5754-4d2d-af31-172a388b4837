@echo off
echo ========================================
echo        بناء تطبيق الصيدلية APK
echo ========================================
echo.

echo 🔧 تنظيف المشروع...
flutter clean

echo 📦 تحميل المكتبات...
flutter pub get

echo 🏗️ بناء APK...
flutter build apk --release

echo.
echo ✅ تم بناء APK بنجاح!
echo 📱 مكان الملف: build\app\outputs\flutter-apk\app-release.apk
echo.

echo 📋 معلومات مهمة:
echo - تأكد من أن الخادم يعمل على الكمبيوتر
echo - تأكد من أن الهاتف والكمبيوتر على نفس الشبكة
echo - IP الحالي في التطبيق: ************
echo - لتغيير IP: عدل ملف lib/config/app_config.dart
echo.

pause
