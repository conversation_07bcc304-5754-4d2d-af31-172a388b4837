const http = require('http');

// بيانات تسجيل الدخول للعميل
const customerLoginData = {
  email: '<EMAIL>',
  password: 'password123'
};

// بيانات تسجيل الدخول للصيدلية
const pharmacyLoginData = {
  email: '<EMAIL>',
  password: 'password123'
};

async function makeRequest(path, method = 'GET', data = null, token = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          resolve({ statusCode: res.statusCode, data: response });
        } catch (e) {
          resolve({ statusCode: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function createTestOrder() {
  try {
    console.log('🔐 تسجيل دخول العميل...');
    const customerLogin = await makeRequest('/api/auth/login', 'POST', customerLoginData);
    
    if (customerLogin.statusCode !== 200) {
      console.log('❌ فشل تسجيل دخول العميل:', customerLogin.data);
      return;
    }
    
    const customerToken = customerLogin.data.token;
    console.log('✅ تم تسجيل دخول العميل بنجاح');

    console.log('🔐 تسجيل دخول الصيدلية...');
    const pharmacyLogin = await makeRequest('/api/auth/login', 'POST', pharmacyLoginData);
    
    if (pharmacyLogin.statusCode !== 200) {
      console.log('❌ فشل تسجيل دخول الصيدلية:', pharmacyLogin.data);
      return;
    }
    
    const pharmacyToken = pharmacyLogin.data.token;
    console.log('✅ تم تسجيل دخول الصيدلية بنجاح');

    // إنشاء طلب دواء
    console.log('📝 إنشاء طلب دواء...');
    const orderData = {
      type: 'medicine_request',
      medicineRequest: {
        medicineName: 'باراسيتامول',
        dosage: '500mg',
        quantity: 2,
        notes: 'للصداع',
        prescriptionImageUrl: 'https://example.com/prescription.jpg'
      },
      deliveryAddress: {
        street: 'شارع التحرير',
        city: 'أسيوط',
        governorate: 'أسيوط',
        postalCode: '71515'
      }
    };

    const createOrder = await makeRequest('/api/orders', 'POST', orderData, customerToken);
    
    if (createOrder.statusCode !== 201) {
      console.log('❌ فشل إنشاء الطلب:', createOrder.data);
      return;
    }
    
    const orderId = createOrder.data.data._id;
    console.log('✅ تم إنشاء الطلب بنجاح:', orderId);

    // إرسال عرض من الصيدلية
    console.log('💰 إرسال عرض من الصيدلية...');
    const offerData = {
      items: [{
        medicineName: 'باراسيتامول',
        dosage: '500mg',
        quantity: 2,
        unitPrice: 15,
        totalPrice: 30
      }],
      totalPrice: 30,
      deliveryFee: 10,
      estimatedDeliveryTime: '30 دقيقة',
      notes: 'متوفر حالياً'
    };

    const sendOffer = await makeRequest(`/api/orders/${orderId}/offers`, 'POST', offerData, pharmacyToken);
    
    if (sendOffer.statusCode !== 201) {
      console.log('❌ فشل إرسال العرض:', sendOffer.data);
      return;
    }
    
    console.log('✅ تم إرسال العرض بنجاح');

    // قبول العرض من العميل
    console.log('✅ قبول العرض من العميل...');
    const acceptOffer = await makeRequest(`/api/orders/${orderId}/accept-offer`, 'POST', {
      offerId: sendOffer.data.data._id
    }, customerToken);
    
    if (acceptOffer.statusCode !== 200) {
      console.log('❌ فشل قبول العرض:', acceptOffer.data);
      return;
    }
    
    console.log('🎉 تم قبول العرض بنجاح! الطلب الآن في حالة "مقبول"');
    console.log('📋 معرف الطلب:', orderId);
    
    // التحقق من الطلبات المقبولة للصيدلية
    console.log('🔍 التحقق من الطلبات المقبولة للصيدلية...');
    const acceptedOrders = await makeRequest('/api/orders/pharmacy?status=confirmed', 'GET', null, pharmacyToken);
    
    console.log('📊 الطلبات المقبولة:', JSON.stringify(acceptedOrders.data, null, 2));

  } catch (error) {
    console.error('❌ خطأ:', error);
  }
}

createTestOrder();
