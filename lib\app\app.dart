import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pharmacy/screens/splash_screen.dart';
import 'package:easy_localization/easy_localization.dart';

class MedicineDeliveryApp extends StatefulWidget {
  const MedicineDeliveryApp({super.key});

  @override
  State<MedicineDeliveryApp> createState() => _MedicineDeliveryAppState();
}

class _MedicineDeliveryAppState extends State<MedicineDeliveryApp> {
  bool _isDark = true;

  @override
  void initState() {
    super.initState();
    _loadDarkMode();
  }

  Future<void> _loadDarkMode() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDark = prefs.getBool('is_dark_mode') ?? true;
    });
  }

  void _toggleDarkMode() async {
    setState(() {
      _isDark = !_isDark;
    });
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('is_dark_mode', _isDark);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData.light(),
      darkTheme: ThemeData.dark(),
      themeMode: _isDark ? ThemeMode.dark : ThemeMode.light,
      locale: context.locale,
      supportedLocales: context.supportedLocales,
      localizationsDelegates: context.localizationDelegates,
      home: SplashScreen(
        isDark: _isDark,
        onToggleDarkMode: _toggleDarkMode,
      ),
    );
  }
}
