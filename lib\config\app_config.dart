import 'package:flutter/foundation.dart';
import '../utils/network_discovery.dart';

class AppConfig {
  // إعدادات الخادم
  static const String _localhost = 'localhost';
  static const String _port = '3000';

  // IP الخادم المكتشف تلقائياً
  static String? _discoveredServerIP;

  // IP احتياطي (يمكن تغييره يدوياً إذا لزم الأمر)
  static const String _fallbackIP = '************';

  // تحديد ما إذا كان التطبيق يعمل على المحاكي أم الهاتف الحقيقي
  static bool get isRunningOnDevice {
    // إذا كان على الويب، استخدم localhost
    if (kIsWeb) return false;
    // للتطبيق المنزل (APK) دائماً true
    return true;
  }

  /// اكتشاف IP الخادم تلقائياً
  static Future<void> discoverServer() async {
    print('🔍 بدء اكتشاف الخادم تلقائياً...');
    _discoveredServerIP = await NetworkDiscovery.discoverServerIP();

    if (_discoveredServerIP != null) {
      print('✅ تم اكتشاف الخادم: $_discoveredServerIP');
    } else {
      print('⚠️ لم يتم اكتشاف الخادم، سيتم استخدام IP الاحتياطي: $_fallbackIP');
    }
  }

  /// الحصول على IP الخادم (المكتشف أو الاحتياطي)
  static String get serverIP {
    return _discoveredServerIP ?? _fallbackIP;
  }

  // العنوان الأساسي للـ API
  static String get baseUrl {
    final host = isRunningOnDevice ? serverIP : _localhost;
    return 'http://$host:$_port/api';
  }
  
  // إعدادات الشبكة
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  
  // إعدادات التطبيق
  static const String appName = 'صيدلية التوصيل';
  static const String appVersion = '1.0.0';
  
  // مفاتيح SharedPreferences
  static const String keyAccessToken = 'access_token';
  static const String keyRefreshToken = 'refresh_token';
  static const String keyUserData = 'user_data';
  static const String keyDarkMode = 'user_dark_mode';
  static const String keyIsAuthenticated = 'is_authenticated';
  
  // رسائل الخطأ
  static const String networkErrorMessage = 'خطأ في الاتصال بالشبكة';
  static const String serverErrorMessage = 'خطأ في الخادم';
  static const String timeoutErrorMessage = 'انتهت مهلة الاتصال';
  static const String unknownErrorMessage = 'حدث خطأ غير معروف';
  
  // معلومات التشخيص
  static void printNetworkInfo() {
    print('=== معلومات الشبكة ===');
    print('Base URL: $baseUrl');
    print('Running on device: $isRunningOnDevice');
    print('Server IP: $serverIP');
    print('Discovered IP: $_discoveredServerIP');
    print('Fallback IP: $_fallbackIP');
    print('Port: $_port');
    print('====================');
  }
}
