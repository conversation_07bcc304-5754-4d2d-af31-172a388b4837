import 'package:flutter/material.dart';

import 'package:pharmacy/screens/home_screen.dart';
import 'package:easy_localization/easy_localization.dart';

const Color kInDriveGreen = Color(0xFF00BF63);
const Color kDarkGrey = Color(0xFF1B1B1B);

class AuthScreen extends StatefulWidget {
  final VoidCallback onAuthenticated;
  final bool isDark;
  final VoidCallback? onToggleDarkMode;
  const AuthScreen({
    super.key,
    required this.onAuthenticated,
    this.isDark = true,
    this.onToggleDarkMode,
  });

  @override
  _AuthScreenState createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isLogin = true;
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = widget.isDark ? kDarkGrey : Colors.white;
    final textColor = widget.isDark ? Colors.white : kDarkGrey;
    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        backgroundColor: bgColor,
        elevation: 0,
        iconTheme: IconThemeData(color: kInDriveGreen),
        title: Text(
          _isLogin ? 'تسجيل الدخول' : 'إنشاء حساب جديد',
          style: TextStyle(
            color: textColor,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Stack(
        children: [
          Center(
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 500),
              switchInCurve: Curves.easeInOut,
              child: _isLogin
                  ? _buildLoginForm(textColor, bgColor)
                  : _buildRegisterForm(textColor, bgColor),
              transitionBuilder: (child, animation) {
                return ScaleTransition(
                  scale: animation,
                  child: FadeTransition(opacity: animation, child: child),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginForm(Color textColor, Color bgColor) {
    return SingleChildScrollView(
      child: Card(
        key: const ValueKey('login'),
        elevation: 20,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
        margin: const EdgeInsets.all(20),
        color: bgColor,
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                FadeTransition(
                  opacity: _animationController,
                  child: SlideTransition(
                    position:
                        Tween<Offset>(
                          begin: const Offset(0, -0.5),
                          end: Offset.zero,
                        ).animate(
                          CurvedAnimation(
                            parent: _animationController,
                            curve: Curves.easeOut,
                          ),
                        ),
                    child: Text(
                      'تسجيل الدخول',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: kInDriveGreen,
                        fontFamily: 'Poppins',
                        fontSize: 22,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 40),
                _buildInputField(
                  _emailController,
                  'البريد الإلكتروني',
                  Icons.email,
                  textColor,
                  bgColor,
                ),
                const SizedBox(height: 20),
                _buildInputField(
                  _passwordController,
                  'كلمة المرور',
                  Icons.lock,
                  textColor,
                  bgColor,
                  isPassword: true,
                ),
                const SizedBox(height: 30),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: kInDriveGreen,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      textStyle: const TextStyle(
                        fontSize: 18,
                        fontFamily: 'Poppins',
                      ),
                      elevation: 4,
                    ),
                    onPressed: _handleLogin,
                    child: const Text('دخول'),
                  ),
                ),
                const SizedBox(height: 20),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _isLogin = false;
                    });
                  },
                  child: Text(
                    'ليس لديك حساب؟ سجل الآن',
                    style: TextStyle(
                      color: kInDriveGreen,
                      fontSize: 16,
                      fontFamily: 'Poppins',
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRegisterForm(Color textColor, Color bgColor) {
    return SingleChildScrollView(
      child: Card(
        key: const ValueKey('register'),
        elevation: 20,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
        margin: const EdgeInsets.all(20),
        color: bgColor,
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                FadeTransition(
                  opacity: _animationController,
                  child: SlideTransition(
                    position:
                        Tween<Offset>(
                          begin: const Offset(0, -0.5),
                          end: Offset.zero,
                        ).animate(
                          CurvedAnimation(
                            parent: _animationController,
                            curve: Curves.easeOut,
                          ),
                        ),
                    child: Text(
                      'إنشاء حساب جديد',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: kInDriveGreen,
                        fontFamily: 'Poppins',
                        fontSize: 22,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 40),
                _buildInputField(
                  _emailController,
                  'البريد الإلكتروني',
                  Icons.email,
                  textColor,
                  bgColor,
                ),
                const SizedBox(height: 20),
                _buildInputField(
                  _passwordController,
                  'كلمة المرور',
                  Icons.lock,
                  textColor,
                  bgColor,
                  isPassword: true,
                ),
                const SizedBox(height: 30),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: kInDriveGreen,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      textStyle: const TextStyle(
                        fontSize: 18,
                        fontFamily: 'Poppins',
                      ),
                      elevation: 4,
                    ),
                    onPressed: _handleRegister,
                    child: const Text('تسجيل'),
                  ),
                ),
                const SizedBox(height: 20),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _isLogin = true;
                    });
                  },
                  child: Text(
                    'لديك حساب؟ سجل دخولك',
                    style: TextStyle(
                      color: kInDriveGreen,
                      fontSize: 16,
                      fontFamily: 'Poppins',
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInputField(
    TextEditingController controller,
    String label,
    IconData icon,
    Color textColor,
    Color bgColor, {
    bool isPassword = false,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: isPassword,
      style: TextStyle(color: textColor, fontFamily: 'Poppins'),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(color: textColor),
        prefixIcon: Icon(icon, color: kInDriveGreen),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(30)),
        filled: true,
        fillColor: bgColor == kDarkGrey ? kDarkGrey : Colors.grey[50],
      ),
      validator: (v) => v!.isEmpty ? 'required'.tr() : null,
    );
  }

  Future<void> _handleLogin() async {
    if (_formKey.currentState!.validate()) {
      await Future.delayed(const Duration(seconds: 1));
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (_) => HomeScreen(
            isDark: widget.isDark,
            onToggleDarkMode: widget.onToggleDarkMode ?? () {},
          ),
        ),
      );
    }
  }

  Future<void> _handleRegister() async {
    if (_formKey.currentState!.validate()) {
      await Future.delayed(const Duration(seconds: 1));
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (_) => HomeScreen(
            isDark: widget.isDark,
            onToggleDarkMode: widget.onToggleDarkMode ?? () {},
          ),
        ),
      );
    }
  }
}
