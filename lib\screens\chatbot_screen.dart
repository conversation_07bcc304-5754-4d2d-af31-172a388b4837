import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

// نموذج الرسالة
class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.text,
    required this.isUser,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

class ChatBotScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;

  const ChatBotScreen({
    super.key,
    required this.isDark,
    required this.onToggleDarkMode,
  });

  @override
  State<ChatBotScreen> createState() => _ChatBotScreenState();
}

class _ChatBotScreenState extends State<ChatBotScreen>
    with TickerProviderStateMixin {
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  bool _isLoading = false;
  late AnimationController _typingAnimationController;

  static const String _apiKey = 'AIzaSyA1XvPyyL7kCco-Bl48Xb4rcXnFrAncuU4';

  // اقتراحات سريعة للأسئلة الشائعة
  final List<Map<String, dynamic>> _quickSuggestions = [
    {'text': 'ما هي جرعة الباراسيتامول؟', 'icon': Icons.medication},
    {'text': 'أعراض جانبية للأسبرين', 'icon': Icons.warning_amber},
    {'text': 'تداخل الأدوية مع الطعام', 'icon': Icons.restaurant},
    {'text': 'نصائح لحفظ الأدوية', 'icon': Icons.storage},
    {'text': 'بدائل المضادات الحيوية', 'icon': Icons.swap_horiz},
    {'text': 'أدوية الضغط والسكري', 'icon': Icons.favorite},
  ];

  @override
  void initState() {
    super.initState();

    // تهيئة انيميشن الكتابة
    _typingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    // إضافة رسالة ترحيبية
    _messages.add(ChatMessage(
      text: '''مرحباً بك في مساعد العلاج الذكي! 👋

أنا هنا لمساعدتك في:
• معلومات الأدوية والعلاجات 💊
• الجرعات والاستخدام الآمن ⚖️
• التداخلات الدوائية ⚠️
• النصائح الصحية 🏥
• الأعراض الجانبية 📋

يمكنك سؤالي مثل:
"ما هي جرعة الباراسيتامول للأطفال؟"
"هل يمكن تناول الأسبرين مع الوارفارين؟"
"ما هي بدائل دواء الضغط؟"

⚠️ تذكر: المعلومات للإرشاد فقط ولا تغني عن استشارة الطبيب المختص.''',
      isUser: false,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    _scrollController.dispose();
    _typingAnimationController.dispose();
    super.dispose();
  }

  Future<void> _sendMessage([String? customText]) async {
    final text = customText ?? _controller.text.trim();
    if (text.isEmpty) return;

    setState(() {
      _messages.add(ChatMessage(text: text, isUser: true));
      _isLoading = true;
      if (customText == null) _controller.clear();
    });

    // تمرير تلقائي للأسفل
    _scrollToBottom();

    final reply = await _getGeminiResponse(text);
    setState(() {
      _messages.add(ChatMessage(text: reply, isUser: false));
      _isLoading = false;
    });

    // تمرير تلقائي للأسفل
    _scrollToBottom();
  }

  // إرسال اقتراح سريع
  void _sendQuickSuggestion(String suggestion) {
    _sendMessage(suggestion);
  }

  // تمرير تلقائي للأسفل
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<String> _getGeminiResponse(String prompt) async {
    try {
      // التحقق من الاتصال بالإنترنت أولاً
      print('🔄 إرسال طلب إلى Gemini AI...');

      final url = Uri.parse(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=$_apiKey',
      );

      print('📡 URL: $url');

      final requestBody = {
        'contents': [
          {
            'parts': [
              {
                'text': '''أنت مساعد صيدلي ذكي ومحترف متخصص في تطبيق الصيدلية. مهمتك مساعدة المستخدمين في:

🔹 معلومات الأدوية والعلاجات
🔹 الجرعات المناسبة والاستخدام الآمن
🔹 التداخلات الدوائية والتحذيرات
🔹 النصائح الصحية والوقائية
🔹 الأعراض الجانبية المحتملة
🔹 البدائل الدوائية المتاحة
🔹 نصائح التخزين والحفظ

قواعد مهمة:
- أجب باللغة العربية دائماً
- كن دقيقاً ومفصلاً في إجاباتك
- استخدم الرموز التعبيرية المناسبة
- اذكر التحذيرات المهمة
- إذا كان السؤال خارج النطاق الطبي، اعتذر بأدب

⚠️ تنبيه مهم: المعلومات المقدمة للإرشاد فقط ولا تغني عن استشارة الطبيب أو الصيدلي المختص.

سؤال المستخدم: $prompt''',
              },
            ],
          },
        ],
        'generationConfig': {
          'temperature': 0.7,
          'topK': 40,
          'topP': 0.95,
          'maxOutputTokens': 1024,
        },
        'safetySettings': [
          {
            'category': 'HARM_CATEGORY_HARASSMENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_HATE_SPEECH',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          }
        ],
      };

      print('📤 إرسال البيانات: ${jsonEncode(requestBody).substring(0, 100)}...');

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      ).timeout(const Duration(seconds: 30));

      print('📥 استلام الرد: ${response.statusCode}');
      print('📄 محتوى الرد: ${response.body.substring(0, response.body.length > 200 ? 200 : response.body.length)}...');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final candidates = data['candidates'];

        if (candidates != null && candidates.isNotEmpty) {
          final content = candidates[0]['content'];
          if (content != null && content['parts'] != null && content['parts'].isNotEmpty) {
            final text = content['parts'][0]['text'];
            if (text != null && text is String) {
              print('✅ تم الحصول على الرد بنجاح');
              return text.trim();
            }
          }
        }

        // في حالة عدم وجود محتوى مناسب
        print('⚠️ لا يوجد محتوى مناسب في الرد');
        return '''عذراً، لا أستطيع الإجابة على هذا السؤال حالياً.

يرجى:
• التأكد من أن السؤال متعلق بالأدوية أو الصحة
• إعادة صياغة السؤال بطريقة أوضح
• المحاولة مرة أخرى لاحقاً

💡 يمكنك استخدام الاقتراحات السريعة أعلاه للحصول على أمثلة.''';
      } else if (response.statusCode == 400) {
        print('❌ خطأ 400: طلب غير صحيح');
        return '''خطأ في الطلب. 🔧

يرجى التحقق من:
• صحة السؤال المطروح
• عدم وجود محتوى غير مناسب
• المحاولة مرة أخرى بسؤال مختلف''';
      } else if (response.statusCode == 403) {
        print('❌ خطأ 403: مفتاح API غير صحيح');
        return '''مشكلة في التفويض. 🔑

يرجى التواصل مع الدعم الفني.''';
      } else if (response.statusCode == 404) {
        print('❌ خطأ 404: الخدمة غير متاحة');
        return '''الخدمة غير متاحة حالياً. 🚫

يرجى:
• التحقق من اتصالك بالإنترنت
• المحاولة مرة أخرى لاحقاً
• التواصل مع الدعم الفني إذا استمرت المشكلة''';
      } else if (response.statusCode == 429) {
        print('❌ خطأ 429: تجاوز الحد المسموح');
        return '''تم تجاوز الحد المسموح من الطلبات. ⏰

يرجى الانتظار قليلاً ثم المحاولة مرة أخرى.''';
      } else {
        print('❌ خطأ غير متوقع: ${response.statusCode}');
        return '''حدث خطأ في الخدمة (${response.statusCode}). 🔧

يرجى المحاولة مرة أخرى لاحقاً.''';
      }
    } catch (e) {
      print('❌ خطأ في الاتصال: $e');
      return '''تعذر الاتصال بالخدمة. 📡

تأكد من اتصالك بالإنترنت وحاول مرة أخرى.

تفاصيل الخطأ: ${e.toString()}''';
    }
  }

  // بناء فقاعة الرسالة
  Widget _buildMessageBubble(ChatMessage message) {
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.8,
        ),
        decoration: BoxDecoration(
          color: message.isUser
              ? const Color(0xFF00BF63)
              : (widget.isDark ? Colors.grey[800] : Colors.grey[200]),
          borderRadius: BorderRadius.only(
            topLeft: const Radius.circular(20),
            topRight: const Radius.circular(20),
            bottomLeft: Radius.circular(message.isUser ? 20 : 4),
            bottomRight: Radius.circular(message.isUser ? 4 : 20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!message.isUser)
              Row(
                children: [
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: const Color(0xFF00BF63),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.smart_toy,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'مساعد العلاج',
                    style: TextStyle(
                      color: widget.isDark ? Colors.white70 : Colors.black54,
                      fontFamily: 'Tajawal',
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            if (!message.isUser) const SizedBox(height: 8),
            Text(
              message.text,
              style: TextStyle(
                color: message.isUser
                    ? Colors.white
                    : (widget.isDark ? Colors.white : Colors.black87),
                fontFamily: 'Tajawal',
                fontSize: 15,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = widget.isDark ? const Color(0xFF1B1B1B) : Colors.white;
    final textColor = widget.isDark ? Colors.white : const Color(0xFF1B1B1B);
    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        backgroundColor: bgColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Color(0xFF00BF63)),
        title: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF00BF63),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.smart_toy,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مساعد العلاج الذكي',
                  style: TextStyle(
                    color: textColor,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Tajawal',
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(
              widget.isDark ? Icons.light_mode : Icons.dark_mode,
              color: const Color(0xFF00BF63),
            ),
            onPressed: widget.onToggleDarkMode,
          ),
        ],
      ),
      body: Column(
        children: [

          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final msg = _messages[index];
                return _buildMessageBubble(msg);
              },
            ),
          ),
          // مؤشر الكتابة
          if (_isLoading)
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFF00BF63),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.smart_toy,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: widget.isDark ? Colors.grey[800] : Colors.grey[200],
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'يكتب',
                          style: TextStyle(
                            color: widget.isDark ? Colors.white70 : Colors.black54,
                            fontFamily: 'Tajawal',
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 8),
                        AnimatedBuilder(
                          animation: _typingAnimationController,
                          builder: (context, child) {
                            return Row(
                              children: List.generate(3, (index) {
                                final delay = index * 0.3;
                                final animationValue = (_typingAnimationController.value - delay).clamp(0.0, 1.0);
                                return Container(
                                  margin: const EdgeInsets.symmetric(horizontal: 1),
                                  width: 6,
                                  height: 6,
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF00BF63).withValues(
                                      alpha: 0.3 + (animationValue * 0.7),
                                    ),
                                    shape: BoxShape.circle,
                                  ),
                                );
                              }),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

          // اقتراحات سريعة
          if (_messages.length <= 1) // إظهار الاقتراحات فقط في البداية
            Container(
              height: 50,
              margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _quickSuggestions.length,
                itemBuilder: (context, index) {
                  final suggestion = _quickSuggestions[index];
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: ActionChip(
                      avatar: Icon(
                        suggestion['icon'] as IconData,
                        size: 16,
                        color: const Color(0xFF00BF63),
                      ),
                      label: Text(
                        suggestion['text'] as String,
                        style: const TextStyle(
                          fontSize: 12,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                      onPressed: () => _sendQuickSuggestion(suggestion['text'] as String),
                      backgroundColor: widget.isDark
                          ? Colors.white.withValues(alpha: 0.1)
                          : Colors.grey[100],
                      labelStyle: TextStyle(
                        color: widget.isDark ? Colors.white70 : Colors.black87,
                      ),
                      side: BorderSide(
                        color: const Color(0xFF00BF63).withValues(alpha: 0.3),
                      ),
                    ),
                  );
                },
              ),
            ),
          Padding(
            padding: const EdgeInsets.fromLTRB(12, 0, 12, 18),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    decoration: InputDecoration(
                      hintText: 'اسأل عن علاج أو دواء... 💊',
                      hintStyle: TextStyle(
                        color: textColor.withValues(alpha: 0.5),
                        fontFamily: 'Tajawal',
                      ),
                      filled: true,
                      fillColor: widget.isDark
                          ? const Color(0xFF232323)
                          : Colors.grey[100],
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide.none,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide(
                          color: const Color(0xFF00BF63).withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: const BorderSide(
                          color: Color(0xFF00BF63),
                          width: 2,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 20,
                      ),
                      prefixIcon: Icon(
                        Icons.chat_bubble_outline,
                        color: const Color(0xFF00BF63).withValues(alpha: 0.7),
                      ),
                    ),
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF00BF63),
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF00BF63).withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Icon(Icons.send_rounded, color: Colors.white),
                    onPressed: _isLoading ? null : _sendMessage,
                    iconSize: 24,
                    splashRadius: 25,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}


 