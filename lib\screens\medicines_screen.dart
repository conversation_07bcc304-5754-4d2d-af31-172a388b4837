import 'package:flutter/material.dart';
import 'offers_screen.dart';
import 'home_screen.dart';

const Color kInDriveGreen = Color(0xFF00BF63);
const Color kDarkGrey = Color(0xFF1B1B1B);

class MedicinesScreen extends StatefulWidget {
  final bool isDark;
  final Function(bool) onToggleDarkMode;
  final VoidCallback? onOrderPressed;
  const MedicinesScreen({
    Key? key,
    required this.isDark,
    required this.onToggleDarkMode,
    this.onOrderPressed,
  }) : super(key: key);

  @override
  State<MedicinesScreen> createState() => _MedicinesScreenState();
}

class _Medicine {
  final String name;
  final String image;
  final String description;
  final double price;
  final String category;
  _Medicine({
    required this.name,
    required this.image,
    required this.description,
    required this.price,
    required this.category,
  });
}

class _MedicinesScreenState extends State<MedicinesScreen>
    with SingleTickerProviderStateMixin {
  final List<_Medicine> _allMedicines = [
    _Medicine(
      name: 'Paracetamol',
      image: 'assets/images/paracetamol.png',
      description: 'مسكن وخافض للحرارة',
      price: 10.0,
      category: 'مسكنات',
    ),
    _Medicine(
      name: 'Aspirin',
      image: 'assets/images/aspirin.png',
      description: 'مضاد للالتهابات ومسكن',
      price: 12.5,
      category: 'مسكنات',
    ),
    _Medicine(
      name: 'Vitamin C',
      image: 'assets/images/vitamin_c.png',
      description: 'مكمل غذائي يقوي المناعة',
      price: 8.0,
      category: 'فيتامينات',
    ),
    _Medicine(
      name: 'Amoxicillin',
      image: 'assets/images/amoxicillin.png',
      description: 'مضاد حيوي واسع الطيف',
      price: 20.0,
      category: 'مضادات حيوية',
    ),
  ];
  String _search = '';
  String _selectedCategory = 'الكل';
  late AnimationController _animController;

  List<String> get _categories => [
    'الكل',
    ...{..._allMedicines.map((m) => m.category)},
  ];

  @override
  void initState() {
    super.initState();
    _animController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 900),
    );
    _animController.forward();
  }

  @override
  void dispose() {
    _animController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = widget.isDark ? kDarkGrey : Colors.white;
    final textColor = widget.isDark ? Colors.white : kDarkGrey;
    final filtered = _allMedicines.where((m) {
      final matchesSearch = m.name.toLowerCase().contains(
        _search.toLowerCase(),
      );
      final matchesCategory =
          _selectedCategory == 'الكل' || m.category == _selectedCategory;
      return matchesSearch && matchesCategory;
    }).toList();
    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        backgroundColor: bgColor,
        elevation: 0,
        iconTheme: IconThemeData(color: kInDriveGreen),
        title: Text(
          'الأدوية',
          style: TextStyle(
            color: textColor,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: TextField(
              style: TextStyle(color: textColor, fontFamily: 'Poppins'),
              decoration: InputDecoration(
                hintText: 'ابحث عن دواء...',
                hintStyle: TextStyle(color: textColor.withValues(alpha: 0.5)),
                prefixIcon: const Icon(Icons.search, color: kInDriveGreen),
                filled: true,
                fillColor: widget.isDark ? kDarkGrey : Colors.grey[50],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 0,
                  horizontal: 16,
                ),
              ),
              onChanged: (v) => setState(() => _search = v),
            ),
          ),
          SizedBox(
            height: 48,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: _categories
                  .map(
                    (cat) => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      child: ChoiceChip(
                        label: Text(
                          cat,
                          style: TextStyle(
                            fontFamily: 'Poppins',
                            color: textColor,
                          ),
                        ),
                        selected: _selectedCategory == cat,
                        selectedColor: kInDriveGreen.withValues(alpha: 0.15),
                        backgroundColor: widget.isDark
                            ? kDarkGrey
                            : Colors.grey[200],
                        onSelected: (_) =>
                            setState(() => _selectedCategory = cat),
                        labelStyle: TextStyle(
                          color: _selectedCategory == cat
                              ? kInDriveGreen
                              : textColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  )
                  .toList(),
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: filtered.isEmpty
                ? Center(
                    child: Text(
                      'لا توجد أدوية مطابقة',
                      style: TextStyle(color: textColor, fontFamily: 'Poppins'),
                    ),
                  )
                : ListView.builder(
                    itemCount: filtered.length,
                    itemBuilder: (ctx, i) {
                      final m = filtered[i];
                      final animation =
                          Tween<Offset>(
                            begin: const Offset(1, 0),
                            end: Offset.zero,
                          ).animate(
                            CurvedAnimation(
                              parent: _animController,
                              curve: Interval(
                                i / filtered.length,
                                1,
                                curve: Curves.easeOutBack,
                              ),
                            ),
                          );
                      return FadeTransition(
                        opacity: _animController,
                        child: SlideTransition(
                          position: animation,
                          child: Card(
                            elevation: 7,
                            margin: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 10,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(22),
                            ),
                            color: widget.isDark ? kDarkGrey : Colors.white,
                            shadowColor: kInDriveGreen.withValues(alpha: 0.18),
                            child: Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Row(
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: kInDriveGreen.withValues(alpha: 
                                            0.18,
                                          ),
                                          blurRadius: 12,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                    child: CircleAvatar(
                                      backgroundImage: AssetImage(m.image),
                                      radius: 32,
                                      backgroundColor: kInDriveGreen
                                          .withValues(alpha: 0.08),
                                    ),
                                  ),
                                  const SizedBox(width: 18),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          m.name,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 18,
                                            color: textColor,
                                            fontFamily: 'Poppins',
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          m.description,
                                          style: TextStyle(
                                            color: textColor.withValues(alpha: 0.7),
                                            fontSize: 13,
                                            fontFamily: 'Poppins',
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Text(
                                              '${m.price.toStringAsFixed(1)} ج.م',
                                              style: TextStyle(
                                                color: kInDriveGreen,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 15,
                                                fontFamily: 'Poppins',
                                              ),
                                            ),
                                            const Spacer(),
                                            ElevatedButton.icon(
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: kInDriveGreen,
                                                foregroundColor: Colors.white,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(18),
                                                ),
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      horizontal: 14,
                                                      vertical: 8,
                                                    ),
                                                textStyle: const TextStyle(
                                                  fontFamily: 'Poppins',
                                                  fontSize: 14,
                                                ),
                                                elevation: 0,
                                              ),
                                              icon: const Icon(
                                                Icons.shopping_cart_checkout,
                                                size: 18,
                                              ),
                                              label: const Text('طلب'),
                                              onPressed: () {
                                                if (widget.onOrderPressed !=
                                                    null) {
                                                  widget.onOrderPressed!();
                                                } else {
                                                  ScaffoldMessenger.of(
                                                    context,
                                                  ).showSnackBar(
                                                    SnackBar(
                                                      content: Text(
                                                        'تم إضافة ${m.name} إلى سلة الطلبات',
                                                      ),
                                                      backgroundColor:
                                                          kInDriveGreen,
                                                    ),
                                                  );
                                                }
                                              },
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
