import 'package:flutter/material.dart';
import '../config/app_config.dart';
import '../utils/network_discovery.dart';
import '../utils/network_test.dart';

class NetworkTestScreen extends StatefulWidget {
  const NetworkTestScreen({super.key});

  @override
  State<NetworkTestScreen> createState() => _NetworkTestScreenState();
}

class _NetworkTestScreenState extends State<NetworkTestScreen> {
  bool _isLoading = false;
  String _result = '';
  String _currentServerIP = '';

  @override
  void initState() {
    super.initState();
    _currentServerIP = AppConfig.serverIP;
  }

  Future<void> _testCurrentConnection() async {
    setState(() {
      _isLoading = true;
      _result = 'جاري اختبار الاتصال...';
    });

    try {
      final result = await NetworkTest.testConnection();
      setState(() {
        _result = result['message'] ?? 'لا توجد رسالة';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'خطأ في الاختبار: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _rediscoverServer() async {
    setState(() {
      _isLoading = true;
      _result = 'جاري إعادة اكتشاف الخادم...';
    });

    try {
      await AppConfig.discoverServer();
      setState(() {
        _currentServerIP = AppConfig.serverIP;
        _result = 'تم إعادة اكتشاف الخادم!\nالخادم الحالي: $_currentServerIP';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'خطأ في إعادة الاكتشاف: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testSpecificIP(String ip) async {
    setState(() {
      _isLoading = true;
      _result = 'جاري اختبار $ip...';
    });

    try {
      final result = await NetworkDiscovery.testConnection(ip);
      setState(() {
        _result = result['message'] ?? 'لا توجد رسالة';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'خطأ في اختبار $ip: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الشبكة'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات الخادم الحالي:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('IP الخادم: $_currentServerIP'),
                    Text('URL الكامل: ${AppConfig.baseUrl}'),
                    Text('يعمل على الجهاز: ${AppConfig.isRunningOnDevice}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _testCurrentConnection,
              icon: const Icon(Icons.wifi_find),
              label: const Text('اختبار الاتصال الحالي'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _rediscoverServer,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة اكتشاف الخادم'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'اختبار عناوين محددة:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                '***********',
                '***********0',
                '***********2',
                '***********6',
                '************',
                '*************',
              ].map((ip) => ElevatedButton(
                onPressed: _isLoading ? null : () => _testSpecificIP(ip),
                child: Text(ip),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade100,
                  foregroundColor: Colors.blue.shade800,
                ),
              )).toList(),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'نتيجة الاختبار:',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          if (_isLoading) ...[
                            const SizedBox(width: 8),
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _result.isEmpty ? 'لم يتم إجراء أي اختبار بعد' : _result,
                            style: const TextStyle(fontFamily: 'monospace'),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
