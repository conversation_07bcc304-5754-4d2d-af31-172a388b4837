import 'package:flutter/material.dart';
import 'package:pharmacy/widgets/three_d_card.dart';
import 'order_tracking_screen.dart';
import 'home_screen.dart';
import 'dart:ui';
import 'dart:async';
import 'request_medicine_screen.dart';
import '../services/user_service.dart';
import '../services/order_service.dart';
import '../utils/currency_formatter.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

class OffersScreen extends StatefulWidget {
  final String requestId;
  final bool isDark;
  final VoidCallback onToggleDarkMode;

  const OffersScreen({
    super.key,
    required this.requestId,
    this.isDark = true,
    required this.onToggleDarkMode,
  });

  @override
  State<OffersScreen> createState() => _OffersScreenState();
}

class _OffersScreenState extends State<OffersScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _mapAnimationController;
  late AnimationController _searchAnimationController;
  late AnimationController _offersAnimationController;
  late Animation<double> _mapFadeAnimation;
  late Animation<double> _searchScaleAnimation;
  late Animation<double> _searchRotationAnimation;
  late Animation<Offset> _offersSlideAnimation;
  late Animation<double> _searchHoleAnimation;

  List<Map<String, dynamic>> _offers = [];
  Map<String, dynamic>? _orderData;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  bool _showMap = true;
  bool _isSearching = false;
  bool _showOffers = false;
  Timer? _offersTimer; // Timer للتحديث التلقائي

  String _userGovernorate = 'القاهرة'; // سيتم جلبها من بيانات المستخدم

  // إحداثيات المحافظات المصرية
  final Map<String, LatLng> _governorateCoordinates = {
    'القاهرة': const LatLng(30.0444, 31.2357),
    'الجيزة': const LatLng(30.0131, 31.2089),
    'الإسكندرية': const LatLng(31.2001, 29.9187),
    'أسوان': const LatLng(24.0889, 32.8998),
    'أسيوط': const LatLng(27.1809, 31.1837),
    'البحيرة': const LatLng(30.8481, 30.3436),
    'بني سويف': const LatLng(29.0661, 31.0994),
    'الدقهلية': const LatLng(31.0409, 31.3785),
    'الفيوم': const LatLng(29.3084, 30.8428),
    'الغربية': const LatLng(30.8754, 31.0335),
    'الإسماعيلية': const LatLng(30.5965, 32.2715),
    'كفر الشيخ': const LatLng(31.1107, 30.9388),
    'الأقصر': const LatLng(25.6872, 32.6396),
    'مطروح': const LatLng(31.3543, 27.2373),
    'المنيا': const LatLng(28.0871, 30.7618),
    'المنوفية': const LatLng(30.5972, 30.9876),
    'الوادي الجديد': const LatLng(25.4518, 30.5414),
    'شمال سيناء': const LatLng(30.2824, 33.6176),
    'جنوب سيناء': const LatLng(28.4593, 33.8116),
    'بورسعيد': const LatLng(31.2653, 32.3019),
    'قنا': const LatLng(26.1551, 32.7160),
    'البحر الأحمر': const LatLng(26.0667, 33.8116),
    'الشرقية': const LatLng(30.7327, 31.7195),
    'سوهاج': const LatLng(26.5569, 31.6948),
    'السويس': const LatLng(29.9668, 32.5498),
    'دمياط': const LatLng(31.4165, 31.8133),
  };

  /// تحميل العروض الحقيقية من الخادم
  Future<void> _loadOffers() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _hasError = false;
        _errorMessage = null;
      });
    }

    try {
      final now = DateTime.now().toString().substring(11, 19);
      print('🔍 [$now] جلب العروض للطلب: ${widget.requestId}');
      final result = await OrderService.getOrderOffers(orderId: widget.requestId);

      if (mounted) {
        if (result['success'] == true) {
          final offers = List<Map<String, dynamic>>.from(result['data']['offers'] ?? []);
          print('📊 تم استلام ${offers.length} عروض من الخادم');

          // طباعة تفاصيل العروض للتشخيص
          for (int i = 0; i < offers.length; i++) {
            final offer = offers[i];
            print('عرض ${i + 1}: ${offer['pharmacy']?['name']} - ${offer['price']} ج.م');
          }

          setState(() {
            _orderData = result['data']['order'];
            _offers = offers;
            _isLoading = false;
            _showOffers = offers.isNotEmpty;
          });

          // إذا وصلت عروض، أوقف التحديث التلقائي
          if (offers.isNotEmpty) {
            _stopOffersPolling();
            print('🎉 وصلت ${offers.length} عروض! تم إيقاف التحديث التلقائي');
          }
        } else {
          setState(() {
            _hasError = true;
            _errorMessage = result['message'] ?? 'فشل في تحميل العروض';
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = 'خطأ في الاتصال: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  /// بدء التحديث التلقائي للعروض
  void _startOffersPolling() {
    print('🔄 بدء التحديث التلقائي كل 10 ثوانٍ');
    _offersTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (mounted && _offers.isEmpty) {
        // فقط إذا لم تصل عروض بعد
        print('⏰ تحديث تلقائي - جلب العروض...');
        _loadOffers();
      } else if (!mounted) {
        print('❌ إيقاف التحديث - الصفحة مغلقة');
        timer.cancel();
      } else if (_offers.isNotEmpty) {
        print('✅ إيقاف التحديث - وصلت العروض');
        timer.cancel();
      }
    });
  }

  /// إيقاف التحديث التلقائي
  void _stopOffersPolling() {
    _offersTimer?.cancel();
    _offersTimer = null;
  }

  @override
  void initState() {
    super.initState();
    _loadOffers(); // تحميل العروض عند بدء الشاشة
    _startOffersPolling(); // بدء التحديث التلقائي

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    // تهيئة controllers الخريطة والبحث والعروض
    _mapAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    _offersAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // تهيئة الانيميشنز
    _mapFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mapAnimationController, curve: Curves.easeInOut),
    );

    _searchScaleAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _searchAnimationController, curve: Curves.elasticInOut),
    );

    _searchRotationAnimation = Tween<double>(begin: 0.0, end: 2.0).animate(
      CurvedAnimation(parent: _searchAnimationController, curve: Curves.linear),
    );

    // انيميشن فجوة البحث (من 0 إلى 150 pixel)
    _searchHoleAnimation = Tween<double>(begin: 0.0, end: 150.0).animate(
      CurvedAnimation(parent: _searchAnimationController, curve: Curves.easeInOut),
    );

    // انيميشن انزلاق العروض من الأسفل
    _offersSlideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0), // من الأسفل
      end: Offset.zero, // إلى المكان الطبيعي
    ).animate(CurvedAnimation(
      parent: _offersAnimationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
    _mapAnimationController.forward();

    // بدء عملية البحث مع تأخير
    _startSearchSequence();
  }

  @override
  void dispose() {
    _stopOffersPolling(); // إيقاف التحديث التلقائي
    _animationController.dispose();
    _mapAnimationController.dispose();
    _searchAnimationController.dispose();
    _offersAnimationController.dispose();
    super.dispose();
  }

  // بدء تسلسل البحث مع الانيميشن
  Future<void> _startSearchSequence() async {
    // جلب محافظة المستخدم
    await _loadUserGovernorate();

    // انتظار لإظهار الخريطة
    await Future.delayed(const Duration(milliseconds: 1000));

    // بدء انيميشن البحث
    setState(() {
      _isSearching = true;
    });

    _searchAnimationController.repeat();

    // انتظار انيميشن البحث
    await Future.delayed(const Duration(milliseconds: 3000));

    // إيقاف انيميشن البحث وبدء تحميل العروض
    _searchAnimationController.stop();

    // تحميل العروض
    await _loadOffers();

    // إظهار العروض مع انيميشن الانزلاق
    setState(() {
      _isSearching = false;
      _showOffers = true;
    });

    _offersAnimationController.forward();
  }

  // جلب محافظة المستخدم من البيانات المحفوظة
  Future<void> _loadUserGovernorate() async {
    try {
      final userData = await UserService.getUserData();
      final governorate = userData?['governorate'] ?? 'القاهرة';

      setState(() {
        _userGovernorate = governorate;
      });
    } catch (e) {
      setState(() {
        _userGovernorate = 'القاهرة';
      });
    }
  }



  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor = widget.isDark ? const Color(0xFF121212) : Colors.white;
    final textColor = widget.isDark ? Colors.white : const Color(0xFF121212);
    final cardColor = widget.isDark ? const Color(0xFF1E1E1E) : Colors.white;
    final secondaryTextColor = widget.isDark
        ? Colors.grey[400]
        : Colors.grey[600];

    final bool noRequest = widget.requestId.isEmpty;

    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        backgroundColor: bgColor,
        elevation: 0,
        iconTheme: IconThemeData(color: const Color(0xFF00BF63)),
        title: const Text(
          'العروض',
          style: TextStyle(
            color: Color(0xFF00BF63),
            fontWeight: FontWeight.bold,
            fontFamily: 'Tajawal',
            fontSize: 20,
          ),
        ),
      ),
      body: noRequest
          ? _buildNoRequestMessage(context, textColor)
          : Stack(
              children: [
                // الخريطة (تظهر دائماً)
                _buildMapSearchView(bgColor, textColor),

                // العروض (تنزلق من الأسفل)
                if (_showOffers)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: SlideTransition(
                      position: _offersSlideAnimation,
                      child: _buildOffersOverlay(textColor, cardColor, secondaryTextColor),
                    ),
                  ),
              ],
            ),
    );
  }

  // بناء واجهة الخريطة مع انيميشن البحث
  Widget _buildMapSearchView(Color bgColor, Color textColor) {
    final governorateLocation = _governorateCoordinates[_userGovernorate] ??
                               _governorateCoordinates['القاهرة']!;

    return Stack(
      children: [
        // خريطة OpenStreetMap
        FadeTransition(
          opacity: _mapFadeAnimation,
          child: FlutterMap(
            options: MapOptions(
              initialCenter: governorateLocation,
              initialZoom: 10.0,
              interactionOptions: const InteractionOptions(
                flags: InteractiveFlag.pinchZoom | InteractiveFlag.drag,
              ),
            ),
            children: [
              // طبقة الخريطة
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'com.example.pharmacy',
                maxZoom: 18,
              ),

              // طبقة العلامات
              MarkerLayer(
                markers: [
                  Marker(
                    point: governorateLocation,
                    width: 60,
                    height: 60,
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: const Color(0xFF00BF63),
                        border: Border.all(color: Colors.white, width: 3),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.location_city,
                        color: Colors.white,
                        size: 30,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // فجوة البحث في الخريطة (أزرق شفاف مع النصوص)
        if (_isSearching)
          AnimatedBuilder(
            animation: _searchHoleAnimation,
            builder: (context, child) {
              return Center(
                child: Container(
                  width: _searchHoleAnimation.value * 2,
                  height: _searchHoleAnimation.value * 2,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.blue.withValues(alpha: 0.15), // أزرق شفاف
                    border: Border.all(
                      color: Colors.blue.withValues(alpha: 0.6),
                      width: 3,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withValues(alpha: 0.3),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: _searchHoleAnimation.value > 80
                    ? Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // نص "جاري البحث" أو "في انتظار العروض"
                          Text(
                            _isSearching ? 'جاري البحث' : 'في انتظار العروض من الصيدليات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.withValues(alpha: 0.9),
                              fontFamily: 'Tajawal',
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 8),

                          // اسم المحافظة أو عدد العروض
                          Text(
                            _isSearching
                                ? 'في $_userGovernorate'
                                : 'تم إرسال طلبك للصيدليات في $_userGovernorate',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.blue.withValues(alpha: 0.7),
                              fontFamily: 'Tajawal',
                            ),
                            textAlign: TextAlign.center,
                          ),

                          // إضافة مؤشر تحميل إذا كان في انتظار العروض
                          if (!_isSearching && _offers.isEmpty) ...[
                            const SizedBox(height: 16),
                            const CircularProgressIndicator(
                              color: Colors.blue,
                              strokeWidth: 2,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'التحديث التلقائي كل 10 ثوانٍ',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue.withValues(alpha: 0.6),
                                fontFamily: 'Tajawal',
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'في انتظار العروض من الصيدليات...',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.blue.withValues(alpha: 0.5),
                                fontFamily: 'Tajawal',
                              ),
                            ),
                          ],
                        ],
                      )
                    : null,
                ),
              );
            },
          ),

        // طبقة شفافة مع انيميشن البحث
        Container(
          color: bgColor.withValues(alpha: 0.8),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // انيميشن البحث
                AnimatedBuilder(
                  animation: Listenable.merge([_searchScaleAnimation, _searchRotationAnimation]),
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _searchScaleAnimation.value,
                      child: Transform.rotate(
                        angle: _searchRotationAnimation.value * 3.14159,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                const Color(0xFF00BF63).withValues(alpha: 0.8),
                                const Color(0xFF00BF63).withValues(alpha: 0.3),
                                Colors.transparent,
                              ],
                            ),
                          ),
                          child: const Icon(
                            Icons.search,
                            size: 50,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    );
                  },
                ),


              ],
            ),
          ),
        ),
      ],
    );
  }

  // بناء طبقة العروض التي تنزلق من الأسفل
  Widget _buildOffersOverlay(Color textColor, Color cardColor, Color? secondaryTextColor) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6, // 60% من ارتفاع الشاشة
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        children: [
          // مقبض السحب
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // عنوان العروض
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Icon(
                  Icons.local_offer,
                  color: const Color(0xFF00BF63),
                  size: 24,
                ),
                const SizedBox(width: 10),
                Text(
                  'العروض المتاحة في $_userGovernorate',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: textColor,
                    fontFamily: 'Tajawal',
                  ),
                ),
              ],
            ),
          ),

          // قائمة العروض
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _offers.length,
              itemBuilder: (context, index) {
                final offer = _offers[index];
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: _buildOfferCard(offer, textColor, cardColor),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // بناء كارت العرض
  Widget _buildOfferCard(Map<String, dynamic> offer, Color textColor, Color cardColor) {
    return Card(
      elevation: 4,
      color: cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الصيدلية
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.blue,
                  child: Icon(
                    Icons.local_pharmacy,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        offer['pharmacy']?['name'] ?? 'صيدلية غير محددة',
                        style: TextStyle(
                          color: textColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                      Text(
                        offer['pharmacy']?['phone'] ?? 'لا يوجد رقم',
                        style: TextStyle(
                          color: textColor.withValues(alpha: 0.7),
                          fontSize: 14,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // تفاصيل العرض
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'السعر',
                      style: TextStyle(
                        color: textColor.withValues(alpha: 0.7),
                        fontSize: 12,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                    Text(
                      '${offer['price'] ?? 0} ج.م',
                      style: TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الكمية المتاحة',
                      style: TextStyle(
                        color: textColor.withValues(alpha: 0.7),
                        fontSize: 12,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                    Text(
                      '${offer['availableQuantity'] ?? 0}',
                      style: TextStyle(
                        color: textColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'وقت التوصيل',
                      style: TextStyle(
                        color: textColor.withValues(alpha: 0.7),
                        fontSize: 12,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                    Text(
                      '${offer['estimatedTime'] ?? 30} دقيقة',
                      style: TextStyle(
                        color: textColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                  ],
                ),
              ],
            ),

            // الملاحظات
            if (offer['notes'] != null && offer['notes'].toString().isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'ملاحظات: ${offer['notes']}',
                style: TextStyle(
                  color: textColor.withValues(alpha: 0.8),
                  fontSize: 14,
                  fontFamily: 'Tajawal',
                ),
              ),
            ],

            const SizedBox(height: 16),

            // زر قبول العرض
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _selectOffer(offer),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'قبول العرض',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Tajawal',
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // اختيار عرض معين
  void _selectOffer(Map<String, dynamic> offer) {
    _acceptOffer(offer['_id'] ?? offer['id'] ?? '');
  }







  Widget _buildNoRequestMessage(BuildContext context, Color textColor) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(Icons.local_offer, color: const Color(0xFF00BF63), size: 60),
            const SizedBox(height: 24),
            Text(
              'لا توجد عروض متاحة حالياً',
              style: TextStyle(
                color: textColor,
                fontSize: 20,
                fontWeight: FontWeight.bold,
                fontFamily: 'Tajawal',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'قم بطلب دواء أولاً لعرض العروض المتاحة من الصيدليات القريبة.',
              style: TextStyle(
                color: textColor.withValues(alpha: 0.7),
                fontSize: 15,
                fontFamily: 'Tajawal',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 28),
            ElevatedButton.icon(
              icon: const Icon(Icons.add_circle_outline),
              label: const Text('طلب دواء'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF00BF63),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 14,
                ),
                textStyle: const TextStyle(
                  fontFamily: 'Tajawal',
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              onPressed: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (_) => RequestMedicineScreen(
                      isDark: widget.isDark,
                      onToggleDarkMode: widget.onToggleDarkMode,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _acceptOffer(String offerId) async {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          backgroundColor: widget.isDark
              ? const Color(0xFF1E1E1E)
              : Colors.white,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(color: Color(0xFF00BF63)),
              const SizedBox(height: 20),
              Text(
                'جاري تأكيد الطلب...',
                style: TextStyle(
                  color: widget.isDark ? Colors.white : const Color(0xFF121212),
                  fontFamily: 'Tajawal',
                ),
              ),
            ],
          ),
        ),
      );

      // استدعاء API لقبول العرض
      final result = await OrderService.acceptOffer(
        orderId: widget.requestId,
        offerId: offerId,
      );

      if (!mounted) return;

      Navigator.pop(context); // Close loading dialog

      if (result['success'] == true) {
        // إظهار رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              result['message'] ?? 'تم قبول العرض بنجاح',
              style: const TextStyle(fontFamily: 'Tajawal'),
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // الانتقال لتتبع الطلب الاحترافي
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => OrderTrackingScreen(
              orderId: widget.requestId, // تمرير ID الطلب للتتبع الحقيقي
              isDark: widget.isDark,
              onToggleDarkMode: widget.onToggleDarkMode,
              pharmacyAddress: result['data']['pharmacyName'] ?? 'الصيدلية',
              userAddress: 'موقعك الحالي',
              order: result['data']['order'], // تمرير بيانات الطلب الكاملة من الخادم
            ),
          ),
        );
      } else {
        // إظهار رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              result['message'] ?? 'فشل في قبول العرض',
              style: const TextStyle(fontFamily: 'Tajawal'),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // إذا كانت المشكلة في المصادقة، أظهر رسالة خاصة
        if (result['needLogin'] == true) {
          _showLoginRequiredDialog();
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// إظهار حوار يطلب تسجيل الدخول
  void _showLoginRequiredDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.login,
                color: Colors.orange,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'تسجيل الدخول مطلوب',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                  fontFamily: 'Tajawal',
                ),
              ),
            ],
          ),
          content: Text(
            'انتهت صلاحية جلسة المستخدم. يرجى تسجيل الدخول مرة أخرى لمتابعة العملية.',
            style: TextStyle(fontSize: 16, fontFamily: 'Tajawal'),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context); // إغلاق الحوار
              },
              child: Text(
                'إلغاء',
                style: TextStyle(color: Colors.grey, fontFamily: 'Tajawal'),
              ),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context); // إغلاق الحوار
                Navigator.pop(context); // العودة للصفحة السابقة
                // يمكن إضافة الانتقال لصفحة تسجيل الدخول هنا
                // Navigator.pushNamed(context, '/login');
              },
              icon: const Icon(Icons.login, size: 18),
              label: Text(
                'تسجيل الدخول',
                style: TextStyle(fontFamily: 'Tajawal'),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

class OfferCard extends StatefulWidget {
  final Map<String, dynamic> offer;
  final VoidCallback onAccept;
  final bool isDark;

  const OfferCard({
    super.key,
    required this.offer,
    required this.onAccept,
    required this.isDark,
  });

  @override
  State<OfferCard> createState() => _OfferCardState();
}

class _OfferCardState extends State<OfferCard> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    final cardColor = widget.isDark ? const Color(0xFF1E1E1E) : Colors.white;
    final textColor = widget.isDark ? Colors.white : const Color(0xFF121212);
    final secondaryTextColor = widget.isDark
        ? Colors.grey[400]
        : Colors.grey[600];
    final discountColor = const Color(0xFFE53935);

    return GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      onTap: widget.onAccept,
      child: AnimatedScale(
        duration: const Duration(milliseconds: 100),
        scale: _isPressed ? 0.98 : 1.0,
        child: Container(
          decoration: BoxDecoration(
            color: cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Pharmacy Info Row
              Row(
                children: [
                  // Pharmacy Image
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child:
                          widget.offer['pharmacy_image'].toString().startsWith(
                            'http',
                          )
                          ? Image.network(
                              widget.offer['pharmacy_image'],
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Image.asset(
                                  'assets/images/pharmacy.png',
                                  fit: BoxFit.cover,
                                );
                              },
                            )
                          : Image.asset(
                              widget.offer['pharmacy_image'],
                              fit: BoxFit.cover,
                            ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Pharmacy Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.offer['pharmacy'],
                          style: TextStyle(
                            color: textColor,
                            fontFamily: 'Tajawal',
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(Icons.star, color: Colors.amber, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              widget.offer['rating'].toString(),
                              style: TextStyle(
                                color: secondaryTextColor,
                                fontFamily: 'Tajawal',
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Icon(
                              Icons.location_on_outlined,
                              color: const Color(0xFF00BF63),
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              widget.offer['distance'],
                              style: TextStyle(
                                color: secondaryTextColor,
                                fontFamily: 'Tajawal',
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.offer['details'],
                          style: TextStyle(
                            color: const Color(0xFF00BF63),
                            fontFamily: 'Tajawal',
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Price and Delivery Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Price Column
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'السعر',
                        style: TextStyle(
                          color: secondaryTextColor,
                          fontFamily: 'Tajawal',
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '${widget.offer['price']} ج.م',
                            style: TextStyle(
                              color: textColor,
                              fontFamily: 'Tajawal',
                              fontWeight: FontWeight.bold,
                              fontSize: 20,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (widget.offer['original_price'] != null)
                            Text(
                              '${widget.offer['original_price']} ج.م',
                              style: TextStyle(
                                color: discountColor,
                                fontFamily: 'Tajawal',
                                decoration: TextDecoration.lineThrough,
                                fontSize: 14,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),

                  // Delivery Column
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'وقت التوصيل',
                        style: TextStyle(
                          color: secondaryTextColor,
                          fontFamily: 'Tajawal',
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.offer['deliveryTime'],
                        style: TextStyle(
                          color: textColor,
                          fontFamily: 'Tajawal',
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Commission Info
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF00BF63).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: const Color(0xFF00BF63),
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'يشمل عمولة ${widget.offer['commission']}',
                      style: TextStyle(
                        color: const Color(0xFF00BF63),
                        fontFamily: 'Tajawal',
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Accept Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: widget.onAccept,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF00BF63),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 14),
                  ),
                  child: const Text(
                    'قبول العرض',
                    style: TextStyle(
                      fontFamily: 'Tajawal',
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
