import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;
import 'dart:async';
import '../services/order_service.dart';
import 'home_screen.dart';

class OrderTrackingScreen extends StatefulWidget {
  final bool isDark;
  final String? pharmacyAddress;
  final String? userAddress;
  final VoidCallback onToggleDarkMode;
  final String? orderId;
  final Map<String, dynamic>? order;

  const OrderTrackingScreen({
    Key? key,
    required this.isDark,
    this.pharmacyAddress,
    this.userAddress,
    required this.onToggleDarkMode,
    this.orderId,
    this.order,
  }) : super(key: key);

  @override
  State<OrderTrackingScreen> createState() => _OrderTrackingScreenState();
}

class _OrderTrackingScreenState extends State<OrderTrackingScreen>
    with TickerProviderStateMixin {
  // Animation Controllers
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _progressController;
  late AnimationController _floatingController;

  // Animations
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _progressAnimation;
  late Animation<double> _floatingAnimation;

  // State variables
  int _estimatedTime = 15;
  String _currentStatus = 'out_for_delivery';
  Timer? _statusTimer;
  Timer? _dataRefreshTimer;
  Map<String, dynamic>? _currentOrder;
  bool _isLoading = false;
  String? _errorMessage;

  // Order Status Steps
  final List<Map<String, dynamic>> _orderSteps = [
    {
      'key': 'confirmed',
      'title': 'تم تأكيد الطلب',
      'subtitle': 'تم قبول طلبك من الصيدلية',
      'icon': Icons.check_circle,
      'color': const Color(0xFF27AE60),
    },
    {
      'key': 'preparing',
      'title': 'قيد التجهيز',
      'subtitle': 'جاري تحضير الأدوية',
      'icon': Icons.medical_services,
      'color': const Color(0xFFF39C12),
    },
    {
      'key': 'driver_assigned',
      'title': 'تم تعيين السائق',
      'subtitle': 'تم تعيين سائق لتوصيل طلبك',
      'icon': Icons.person,
      'color': const Color(0xFF3498DB),
    },
    {
      'key': 'ready',
      'title': 'جاهز للتسليم',
      'subtitle': 'الطلب جاهز وفي انتظار السائق',
      'icon': Icons.inventory,
      'color': const Color(0xFF9B59B6),
    },
    {
      'key': 'out_for_delivery',
      'title': 'جاري التوصيل',
      'subtitle': 'السائق في الطريق إليك',
      'icon': Icons.local_shipping,
      'color': const Color(0xFFE67E22),
    },
    {
      'key': 'delivered',
      'title': 'تم الاستلام',
      'subtitle': 'تم تسليم الطلب بنجاح',
      'icon': Icons.done_all,
      'color': const Color(0xFF27AE60),
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeOrderData();
    _startStatusTimer();
    _startDataRefreshTimer();
  }

  void _initializeAnimations() {
    // Pulse Animation for active step
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Rotation Animation for loading
    _rotationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    // Slide Animation for steps
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    // Scale Animation for icons
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.bounceOut,
    ));

    // Progress Animation
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    // Floating Animation
    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _floatingAnimation = Tween<double>(
      begin: -10.0,
      end: 10.0,
    ).animate(CurvedAnimation(
      parent: _floatingController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
    _slideController.forward();
    _scaleController.forward();
    _progressController.forward();
    _floatingController.repeat(reverse: true);
  }

  void _initializeOrderData() {
    // استخدم بيانات الطلب الممررة كنقطة بداية
    if (widget.order != null) {
      _currentOrder = Map<String, dynamic>.from(widget.order!);
      print('📋 بيانات الطلب الممررة: ${_currentOrder.toString()}');

      // مزامنة الحالة ووقت التوصيل إذا توفرا
      if (_currentOrder!['status'] != null) {
        _currentStatus = _currentOrder!['status'];
        print('📊 حالة الطلب: $_currentStatus');
      }
      // دعم كل من estimatedDelivery (نص أو رقم)
      if (_currentOrder!['estimatedDelivery'] != null) {
        final deliveryVal = _currentOrder!['estimatedDelivery'];
        if (deliveryVal is int) {
          _estimatedTime = deliveryVal;
        } else if (deliveryVal is String) {
          final timeStr = deliveryVal.split(' ')[0];
          final parsed = int.tryParse(timeStr);
          if (parsed != null) _estimatedTime = parsed;
        }
      }
    } else {
      // إذا لم تتوفر بيانات الطلب، استخدم خريطة فارغة
      _currentOrder = {};
    }

    // جلب البيانات الحقيقية من الخادم فوراً
    if (widget.orderId != null) {
      _fetchRealTimeOrderData();
    }
  }

  void _startStatusTimer() {
    _statusTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted && _currentStatus != 'delivered') {
        setState(() {
          if (_estimatedTime > 0) {
            _estimatedTime--;
          }
        });
      }
    });
  }

  /// بدء مؤقت تحديث البيانات من الخادم
  void _startDataRefreshTimer() {
    _dataRefreshTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (mounted && widget.orderId != null) {
        _fetchRealTimeOrderData();
      }
    });
  }

  /// جلب البيانات الحقيقية للطلب من الخادم
  Future<void> _fetchRealTimeOrderData() async {
    if (widget.orderId == null) return;

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      print('🔄 جلب بيانات الطلب: ${widget.orderId}');

      // التعامل مع الطلبات التجريبية
      if (widget.orderId == 'DEMO-ORDER-123') {
        print('📋 استخدام بيانات تجريبية للطلب');
      }

      // جلب بيانات الطلب المحدد من الخادم
      final result = await OrderService.getOrderById(
        orderId: widget.orderId!,
      );

      if (result['success'] == true && result['data'] != null) {
        final orderData = result['data'];
        print('📋 بيانات الطلب المستلمة: ${orderData.toString()}');

        setState(() {
          _currentOrder = Map<String, dynamic>.from(orderData);

          // تحديث الحالة الحالية
          final newStatus = _currentOrder!['status'] ?? 'pending';
          print('📊 حالة الطلب: $newStatus');

          // إذا تغيرت الحالة، أظهر إشعار للمستخدم
          if (_currentStatus != newStatus && _currentStatus.isNotEmpty) {
            _showStatusChangeNotification(_currentStatus, newStatus);
          }

          _currentStatus = newStatus;

          // تحديث الوقت المتوقع للتوصيل
          if (_currentOrder!['delivery'] != null &&
              _currentOrder!['delivery']['estimatedTime'] != null) {
            final estimatedTime = _currentOrder!['delivery']['estimatedTime'];
            if (estimatedTime is String) {
              final now = DateTime.now();
              final estimated = DateTime.parse(estimatedTime);
              final difference = estimated.difference(now).inMinutes;
              if (difference > 0) {
                _estimatedTime = difference;
              }
            }
          }

          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = result['message'] ?? 'فشل في جلب بيانات الطلب';
          _isLoading = false;
        });

        // إذا كانت المشكلة في المصادقة، أظهر رسالة خاصة
        if (result['needLogin'] == true) {
          _showLoginRequiredDialog();
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في الاتصال بالخادم';
        _isLoading = false;
      });
    }
  }

  /// إظهار حوار يطلب تسجيل الدخول
  void _showLoginRequiredDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.login,
                color: Colors.orange,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'تسجيل الدخول مطلوب',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          content: Text(
            'انتهت صلاحية جلسة المستخدم. يرجى تسجيل الدخول مرة أخرى لمتابعة تتبع الطلب.',
            style: GoogleFonts.cairo(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context); // إغلاق الحوار
                Navigator.pop(context); // العودة للصفحة السابقة
              },
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(color: Colors.grey),
              ),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context); // إغلاق الحوار
                Navigator.pop(context); // العودة للصفحة السابقة
                // يمكن إضافة الانتقال لصفحة تسجيل الدخول هنا
                // Navigator.pushNamed(context, '/login');
              },
              icon: const Icon(Icons.login, size: 18),
              label: Text(
                'تسجيل الدخول',
                style: GoogleFonts.cairo(),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// إظهار إشعار تغيير حالة الطلب
  void _showStatusChangeNotification(String oldStatus, String newStatus) {
    final statusMessages = {
      'pending': 'في انتظار الموافقة',
      'confirmed': 'تم تأكيد الطلب',
      'preparing': 'جاري التجهيز',
      'ready': 'جاهز للاستلام',
      'driver_assigned': 'تم تعيين سائق',
      'out_for_delivery': 'في الطريق إليك',
      'delivered': 'تم التوصيل',
      'cancelled': 'تم الإلغاء',
    };

    final newStatusText = statusMessages[newStatus] ?? newStatus;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              _getStatusIcon(newStatus),
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Text(
                'تحديث: $newStatusText',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: _getStatusColor(newStatus),
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  /// الحصول على أيقونة الحالة
  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'confirmed':
        return Icons.check_circle;
      case 'preparing':
        return Icons.kitchen;
      case 'ready':
        return Icons.inventory;
      case 'driver_assigned':
        return Icons.person;
      case 'out_for_delivery':
        return Icons.local_shipping;
      case 'delivered':
        return Icons.done_all;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(String status) {
    switch (status) {
      case 'confirmed':
        return const Color(0xFF3498DB);
      case 'preparing':
        return const Color(0xFFF39C12);
      case 'ready':
        return const Color(0xFF9B59B6);
      case 'driver_assigned':
        return const Color(0xFF1ABC9C);
      case 'out_for_delivery':
        return const Color(0xFF2ECC71);
      case 'delivered':
        return const Color(0xFF27AE60);
      case 'cancelled':
        return const Color(0xFFE74C3C);
      default:
        return const Color(0xFF95A5A6);
    }
  }

  /// الحصول على اسم السائق من البيانات الحقيقية
  String _getDriverName() {
    if (_currentOrder == null) return 'غير محدد';

    print('🔍 البحث عن اسم السائق في: ${_currentOrder!.keys.toList()}');

    // البحث في delivery.driver.name أولاً (البيانات الحقيقية من الخادم)
    if (_currentOrder!['delivery'] != null &&
        _currentOrder!['delivery']['driver'] != null &&
        _currentOrder!['delivery']['driver']['name'] != null) {
      final driverName = _currentOrder!['delivery']['driver']['name'];
      print('👤 اسم السائق من delivery.driver.name: $driverName');
      return driverName;
    }

    // البحث في driver.name مباشرة
    if (_currentOrder!['driver'] != null &&
        _currentOrder!['driver']['name'] != null) {
      final driverName = _currentOrder!['driver']['name'];
      print('👤 اسم السائق من driver.name: $driverName');
      return driverName;
    }

    // البحث في driverName (البيانات المحلية)
    if (_currentOrder!['driverName'] != null) {
      final driverName = _currentOrder!['driverName'];
      print('👤 اسم السائق من driverName: $driverName');
      return driverName;
    }

    // البحث في assignedDriver
    if (_currentOrder!['assignedDriver'] != null &&
        _currentOrder!['assignedDriver']['name'] != null) {
      final driverName = _currentOrder!['assignedDriver']['name'];
      print('👤 اسم السائق من assignedDriver.name: $driverName');
      return driverName;
    }

    print('⚠️ لم يتم العثور على اسم السائق');
    return 'غير محدد';
  }

  /// الحصول على رقم هاتف السائق من البيانات الحقيقية
  String _getDriverPhone() {
    if (_currentOrder == null) return 'غير محدد';

    print('🔍 البحث عن رقم هاتف السائق في: ${_currentOrder!.keys.toList()}');

    // البحث في delivery.driver.phone أولاً (البيانات الحقيقية من الخادم)
    if (_currentOrder!['delivery'] != null &&
        _currentOrder!['delivery']['driver'] != null &&
        _currentOrder!['delivery']['driver']['phone'] != null) {
      final driverPhone = _currentOrder!['delivery']['driver']['phone'];
      print('📞 رقم هاتف السائق من delivery.driver.phone: $driverPhone');
      return driverPhone;
    }

    // البحث في driver.phone مباشرة
    if (_currentOrder!['driver'] != null &&
        _currentOrder!['driver']['phone'] != null) {
      final driverPhone = _currentOrder!['driver']['phone'];
      print('📞 رقم هاتف السائق من driver.phone: $driverPhone');
      return driverPhone;
    }

    // البحث في driverPhone (البيانات المحلية)
    if (_currentOrder!['driverPhone'] != null) {
      final driverPhone = _currentOrder!['driverPhone'];
      print('📞 رقم هاتف السائق من driverPhone: $driverPhone');
      return driverPhone;
    }

    // البحث في assignedDriver
    if (_currentOrder!['assignedDriver'] != null &&
        _currentOrder!['assignedDriver']['phone'] != null) {
      final driverPhone = _currentOrder!['assignedDriver']['phone'];
      print('📞 رقم هاتف السائق من assignedDriver.phone: $driverPhone');
      return driverPhone;
    }

    print('⚠️ لم يتم العثور على رقم هاتف السائق');
    return 'غير محدد';
  }

  /// الحصول على معلومات المركبة من البيانات الحقيقية
  String _getVehicleInfo() {
    if (_currentOrder == null) return 'غير محدد';

    // البحث في delivery.driver.vehicleInfo (البيانات الحقيقية من الخادم)
    if (_currentOrder!['delivery'] != null &&
        _currentOrder!['delivery']['driver'] != null &&
        _currentOrder!['delivery']['driver']['vehicleInfo'] != null) {
      return _currentOrder!['delivery']['driver']['vehicleInfo'];
    }

    return 'دراجة نارية';
  }

  /// بناء قائمة الأدوية من البيانات الحقيقية
  List<Widget> _buildMedicinesList(Color textColor) {
    if (_currentOrder == null) return [];

    List<Widget> medicineWidgets = [];
    print('🔍 البحث عن الأدوية في: ${_currentOrder!.keys.toList()}');

    // البحث في items أولاً (البيانات الحقيقية من الخادم)
    if (_currentOrder!['items'] != null && _currentOrder!['items'] is List) {
      final items = _currentOrder!['items'] as List;
      print('💊 عدد الأدوية الموجودة: ${items.length}');

      for (var item in items) {
        final medicineName = item['medicine']?['name'] ?? item['name'] ?? 'غير محدد';
        final quantity = (item['quantity'] as num?)?.toInt() ?? 1;
        final price = (item['price'] as num?)?.toDouble() ?? 0.0;
        final manufacturer = item['medicine']?['manufacturer'] ?? '';
        final strength = item['medicine']?['strength'] ?? '';

        print('💊 دواء: $medicineName - الكمية: $quantity - السعر: $price');

        medicineWidgets.add(_buildMedicineItem(
          name: medicineName,
          quantity: quantity,
          price: price,
          manufacturer: manufacturer,
          strength: strength,
          textColor: textColor,
        ));
      }
    }
    // البحث في medicines (البيانات المحلية)
    else if (_currentOrder!['medicines'] != null && _currentOrder!['medicines'] is List) {
      final medicines = _currentOrder!['medicines'] as List;
      for (var medicine in medicines) {
        medicineWidgets.add(_buildMedicineItem(
          name: medicine['name'] ?? 'غير محدد',
          quantity: medicine['quantity'] ?? 1,
          price: medicine['price'] ?? 0.0,
          textColor: textColor,
        ));
      }
    }
    // البحث في medicineRequest (للطلبات البسيطة)
    else if (_currentOrder!['medicineRequest'] != null) {
      final request = _currentOrder!['medicineRequest'];
      medicineWidgets.add(_buildMedicineItem(
        name: request['name'] ?? 'غير محدد',
        quantity: request['quantity'] ?? 1,
        price: 0.0, // السعر غير متوفر في الطلبات البسيطة
        textColor: textColor,
      ));
    }

    return medicineWidgets;
  }

  /// بناء عنصر دواء واحد
  Widget _buildMedicineItem({
    required String name,
    required int quantity,
    required double price,
    String? manufacturer,
    String? strength,
    required Color textColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: textColor.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: textColor.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: const Color(0xFF3498DB).withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                Icons.medication,
                color: const Color(0xFF3498DB),
                size: 24,
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: textColor,
                    ),
                  ),
                  if (manufacturer != null && manufacturer.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      'الشركة المصنعة: $manufacturer',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: textColor.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                  if (strength != null && strength.isNotEmpty) ...[
                    const SizedBox(height: 2),
                    Text(
                      'التركيز: $strength',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: textColor.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'الكمية: $quantity',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF2ECC71),
                        ),
                      ),
                      if (price > 0)
                        Text(
                          '${(price * quantity).toStringAsFixed(2)} جنيه',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF27AE60),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على المبلغ الإجمالي من البيانات الحقيقية
  double _getTotalAmount() {
    if (_currentOrder == null) return 0.0;

    print('🔍 البحث عن المبلغ الإجمالي في: ${_currentOrder!.keys.toList()}');

    // البحث في pricing.total أولاً (البيانات الحقيقية من الخادم)
    if (_currentOrder!['pricing'] != null &&
        _currentOrder!['pricing']['total'] != null) {
      final total = (_currentOrder!['pricing']['total'] as num).toDouble();
      print('💰 المبلغ من pricing.total: $total');
      if (total > 0) return total;
    }

    // البحث في totalAmount (البيانات المحلية)
    if (_currentOrder!['totalAmount'] != null) {
      final total = (_currentOrder!['totalAmount'] as num).toDouble();
      print('💰 المبلغ من totalAmount: $total');
      if (total > 0) return total;
    }

    // البحث في total مباشرة
    if (_currentOrder!['total'] != null) {
      final total = (_currentOrder!['total'] as num).toDouble();
      print('💰 المبلغ من total: $total');
      if (total > 0) return total;
    }

    // حساب المبلغ من العروض المقبولة
    if (_currentOrder!['offers'] != null && _currentOrder!['offers'] is List) {
      final offers = _currentOrder!['offers'] as List;
      final acceptedOffer = offers.firstWhere(
        (offer) => offer['status'] == 'accepted',
        orElse: () => null,
      );
      if (acceptedOffer != null && acceptedOffer['price'] != null) {
        final total = (acceptedOffer['price'] as num).toDouble();
        print('💰 المبلغ من العرض المقبول: $total');
        if (total > 0) return total;
      }
    }

    // البحث في acceptedOffer مباشرة
    if (_currentOrder!['acceptedOffer'] != null &&
        _currentOrder!['acceptedOffer']['price'] != null) {
      final total = (_currentOrder!['acceptedOffer']['price'] as num).toDouble();
      print('💰 المبلغ من acceptedOffer.price: $total');
      if (total > 0) return total;
    }

    // حساب المبلغ من الأدوية إذا لم يوجد مبلغ إجمالي
    if (_currentOrder!['items'] != null && _currentOrder!['items'] is List) {
      final items = _currentOrder!['items'] as List;
      double total = 0.0;
      for (var item in items) {
        final price = (item['price'] as num?)?.toDouble() ??
                     (item['unitPrice'] as num?)?.toDouble() ??
                     (item['totalPrice'] as num?)?.toDouble() ?? 0.0;
        final quantity = (item['quantity'] as num?)?.toInt() ?? 1;
        total += price * quantity;
      }
      if (total > 0) {
        print('💰 المبلغ المحسوب من الأدوية: $total');
        return total;
      }
    }

    // البحث في medicineRequest للطلبات البسيطة
    if (_currentOrder!['medicineRequest'] != null) {
      // للطلبات البسيطة، نحتاج للبحث في العروض
      if (_currentOrder!['offers'] != null && _currentOrder!['offers'] is List) {
        final offers = _currentOrder!['offers'] as List;
        if (offers.isNotEmpty) {
          // أخذ أول عرض متاح كمرجع للسعر
          final firstOffer = offers.first;
          if (firstOffer['price'] != null) {
            final total = (firstOffer['price'] as num).toDouble();
            print('💰 المبلغ من أول عرض متاح: $total');
            if (total > 0) return total;
          }
        }
      }
    }

    print('⚠️ لم يتم العثور على مبلغ إجمالي - إرجاع 0.0');
    return 0.0;
  }

  /// الحصول على العملة من البيانات الحقيقية
  String _getCurrency() {
    if (_currentOrder == null) return 'جنيه';

    // البحث في currency (البيانات الحقيقية من الخادم)
    if (_currentOrder!['currency'] != null) {
      final currency = _currentOrder!['currency'];
      switch (currency.toString().toUpperCase()) {
        case 'EGP':
          return 'جنيه';
        case 'SAR':
          return 'ريال';
        case 'USD':
          return 'دولار';
        default:
          return currency;
      }
    }

    return 'جنيه';
  }

  /// بناء تفاصيل التسعير
  Widget _buildPricingDetails(Color textColor) {
    if (_currentOrder == null) return const SizedBox.shrink();

    final pricing = _currentOrder!['pricing'];
    final subtotal = pricing?['subtotal']?.toDouble() ?? 0.0;
    final deliveryFee = pricing?['deliveryFee']?.toDouble() ?? 0.0;
    final total = _getTotalAmount();

    return Column(
      children: [
        // المبلغ الفرعي
        if (subtotal > 0) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'قيمة الأدوية:',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: textColor.withValues(alpha: 0.8),
                ),
              ),
              Text(
                '${subtotal.toStringAsFixed(2)} ${_getCurrency()}',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: textColor.withValues(alpha: 0.8),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
        ],

        // رسوم التوصيل
        if (deliveryFee > 0) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'رسوم التوصيل:',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: textColor.withValues(alpha: 0.8),
                ),
              ),
              Text(
                '${deliveryFee.toStringAsFixed(2)} ${_getCurrency()}',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: textColor.withValues(alpha: 0.8),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Divider(color: textColor.withValues(alpha: 0.3)),
          const SizedBox(height: 8),
        ],

        // المبلغ الإجمالي
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'المبلغ الإجمالي:',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
            Text(
              '${total.toStringAsFixed(2)} ${_getCurrency()}',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF27AE60),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء معلومات إضافية عن الطلب
  Widget _buildOrderInfo(Color textColor) {
    return Column(
      children: [
        // رقم الطلب
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'رقم الطلب:',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: textColor.withValues(alpha: 0.7),
              ),
            ),
            Text(
              _getOrderNumber(),
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: textColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        // تاريخ الطلب
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'تاريخ الطلب:',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: textColor.withValues(alpha: 0.7),
              ),
            ),
            Text(
              _getOrderDate(),
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: textColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        // حالة الدفع
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'حالة الدفع:',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: textColor.withValues(alpha: 0.7),
              ),
            ),
            Text(
              _getPaymentStatus(),
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: _getPaymentStatusColor(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// الحصول على رقم الطلب
  String _getOrderNumber() {
    if (_currentOrder == null) return widget.orderId ?? 'غير محدد';

    // البحث في البيانات الحقيقية من الخادم
    return _currentOrder!['orderNumber'] ??
           _currentOrder!['_id'] ??
           _currentOrder!['id'] ??
           widget.orderId ??
           'غير محدد';
  }

  /// الحصول على تاريخ الطلب
  String _getOrderDate() {
    if (_currentOrder == null) return 'غير محدد';

    String? dateStr = _currentOrder!['createdAt'] ?? _currentOrder!['acceptedAt'];
    if (dateStr != null) {
      try {
        final date = DateTime.parse(dateStr);
        return '${date.day}/${date.month}/${date.year}';
      } catch (e) {
        return dateStr;
      }
    }

    return 'غير محدد';
  }

  /// الحصول على حالة الدفع
  String _getPaymentStatus() {
    if (_currentOrder == null) return 'غير محدد';

    final paymentStatus = _currentOrder!['paymentStatus'];
    switch (paymentStatus) {
      case 'pending':
        return 'في الانتظار';
      case 'paid':
        return 'مدفوع';
      case 'failed':
        return 'فشل';
      case 'refunded':
        return 'مسترد';
      default:
        return 'غير محدد';
    }
  }

  /// الحصول على لون حالة الدفع
  Color _getPaymentStatusColor() {
    if (_currentOrder == null) return Colors.grey;

    final paymentStatus = _currentOrder!['paymentStatus'];
    switch (paymentStatus) {
      case 'pending':
        return Colors.orange;
      case 'paid':
        return const Color(0xFF27AE60);
      case 'failed':
        return Colors.red;
      case 'refunded':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _progressController.dispose();
    _floatingController.dispose();
    _statusTimer?.cancel();
    _dataRefreshTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = widget.isDark ? const Color(0xFF0A0A0A) : const Color(0xFFF8F9FA);
    final textColor = widget.isDark ? Colors.white : const Color(0xFF2C3E50);

    return Scaffold(
      backgroundColor: bgColor,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: widget.isDark
                ? [
                    const Color(0xFF0A0A0A),
                    const Color(0xFF1A1A1A),
                    const Color(0xFF2A2A2A),
                  ]
                : [
                    const Color(0xFFF8F9FA),
                    const Color(0xFFE8F4FD),
                    const Color(0xFFD6EAF8),
                  ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(textColor),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      // عرض مؤشر التحميل أو رسالة الخطأ
                      if (_isLoading)
                        Container(
                          padding: const EdgeInsets.all(20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const CircularProgressIndicator(
                                color: Color(0xFF00BF63),
                              ),
                              const SizedBox(width: 15),
                              Text(
                                'جاري تحديث البيانات...',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  color: textColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      if (_errorMessage != null && !_isLoading)
                        Container(
                          margin: const EdgeInsets.only(bottom: 20),
                          padding: const EdgeInsets.all(15),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.error_outline,
                                color: Colors.red,
                                size: 20,
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                child: Text(
                                  _errorMessage!,
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    color: Colors.red,
                                  ),
                                ),
                              ),
                              IconButton(
                                onPressed: () {
                                  setState(() {
                                    _errorMessage = null;
                                  });
                                  _fetchRealTimeOrderData();
                                },
                                icon: const Icon(
                                  Icons.refresh,
                                  color: Colors.red,
                                  size: 20,
                                ),
                              ),
                            ],
                          ),
                        ),
                      _buildTrackingSteps(textColor),
                      const SizedBox(height: 30),
                      if (_currentStatus == 'driver_assigned' ||
                          _currentStatus == 'out_for_delivery')
                        _buildDriverInfo(textColor),
                      const SizedBox(height: 30),
                      _buildOrderDetails(textColor),
                      const SizedBox(height: 30),
                      if (_currentStatus == 'out_for_delivery')
                        _buildDeliveredButton(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(Color textColor) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: const LinearGradient(
                  colors: [Color(0xFF3498DB), Color(0xFF5DADE2)],
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF3498DB).withValues(alpha: 0.3),
                    blurRadius: 15,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Text(
              'تتبع الطلب',
              style: GoogleFonts.cairo(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              _fetchRealTimeOrderData();
            },
            child: AnimatedBuilder(
              animation: _rotationAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _rotationAnimation.value,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: const LinearGradient(
                        colors: [Color(0xFF27AE60), Color(0xFF2ECC71)],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF27AE60).withValues(alpha: 0.3),
                          blurRadius: 15,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.refresh,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildTrackingSteps(Color textColor) {
    final currentStepIndex = _orderSteps.indexWhere((step) => step['key'] == _currentStatus);

    return Container(
      padding: const EdgeInsets.all(25),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: widget.isDark
              ? [const Color(0xFF1A1A1A), const Color(0xFF2A2A2A)]
              : [Colors.white, const Color(0xFFF8F9FA)],
        ),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: widget.isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.1),
            blurRadius: 20,
            spreadRadius: 5,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مسار الطلب',
            style: GoogleFonts.cairo(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 30),
          // Horizontal Progress Road
          _buildHorizontalProgressRoad(currentStepIndex, textColor),
          const SizedBox(height: 30),
          // Current Status Details
          _buildCurrentStatusDetails(currentStepIndex, textColor),
        ],
      ),
    );
  }

  Widget _buildHorizontalProgressRoad(int currentStepIndex, Color textColor) {
    return SizedBox(
      height: 120,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        physics: const NeverScrollableScrollPhysics(),
        child: Column(
          children: [
            // Progress Road Background
            Container(
              height: 12,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                gradient: LinearGradient(
                  colors: widget.isDark
                      ? [const Color(0xFF2C3E50), const Color(0xFF34495E)]
                      : [const Color(0xFFECF0F1), const Color(0xFFBDC3C7)],
                ),
                boxShadow: [
                  BoxShadow(
                    color: widget.isDark
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.grey.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // Animated Progress Bar
                  AnimatedBuilder(
                    animation: _progressAnimation,
                    builder: (context, child) {
                      final progress = (currentStepIndex + 1) / _orderSteps.length;
                      return Container(
                        width: MediaQuery.of(context).size.width * 0.8 * progress * _progressAnimation.value,
                        height: 12,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFF3498DB),
                              Color(0xFF2ECC71),
                              Color(0xFFF39C12),
                              Color(0xFFE67E22),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF3498DB).withValues(alpha: 0.4),
                              blurRadius: 15,
                              spreadRadius: 3,
                            ),
                          ],
                        ),
                        child: Stack(
                          children: [
                            // Moving shine effect
                            AnimatedBuilder(
                              animation: _rotationController,
                              builder: (context, child) {
                                return Positioned(
                                  left: (MediaQuery.of(context).size.width * 0.8 * progress * _progressAnimation.value * _rotationController.value) - 20,
                                  child: Container(
                                    width: 40,
                                    height: 12,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(6),
                                      gradient: LinearGradient(
                                        colors: [
                                          Colors.white.withValues(alpha: 0.0),
                                          Colors.white.withValues(alpha: 0.3),
                                          Colors.white.withValues(alpha: 0.0),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            // Step Icons Row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: List.generate(_orderSteps.length, (index) {
                final step = _orderSteps[index];
                final isCompleted = index <= currentStepIndex;
                final isActive = index == currentStepIndex;

                return Expanded(
                  child: Column(
                    children: [
                      AnimatedBuilder(
                        animation: isActive ? _pulseAnimation : _scaleAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: isActive ? _pulseAnimation.value : 1.0,
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                // Outer glow ring for active step
                                if (isActive)
                                  Container(
                                    width: 60,
                                    height: 60,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      gradient: RadialGradient(
                                        colors: [
                                          step['color'].withValues(alpha: 0.0),
                                          step['color'].withValues(alpha: 0.2),
                                          step['color'].withValues(alpha: 0.4),
                                        ],
                                      ),
                                    ),
                                  ),
                                // Main icon container
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: isCompleted
                                          ? [
                                              step['color'],
                                              step['color'].withValues(alpha: 0.8),
                                              step['color'].withValues(alpha: 0.9),
                                            ]
                                          : [
                                              Colors.grey.withValues(alpha: 0.3),
                                              Colors.grey.withValues(alpha: 0.2),
                                              Colors.grey.withValues(alpha: 0.1),
                                            ],
                                    ),
                                    boxShadow: [
                                      if (isActive) ...[
                                        BoxShadow(
                                          color: step['color'].withValues(alpha: 0.6),
                                          blurRadius: 20,
                                          spreadRadius: 5,
                                          offset: const Offset(0, 5),
                                        ),
                                        BoxShadow(
                                          color: step['color'].withValues(alpha: 0.3),
                                          blurRadius: 30,
                                          spreadRadius: 10,
                                          offset: const Offset(0, 10),
                                        ),
                                      ] else if (isCompleted) ...[
                                        BoxShadow(
                                          color: step['color'].withValues(alpha: 0.3),
                                          blurRadius: 12,
                                          spreadRadius: 2,
                                          offset: const Offset(0, 3),
                                        ),
                                      ] else ...[
                                        BoxShadow(
                                          color: Colors.grey.withValues(alpha: 0.2),
                                          blurRadius: 8,
                                          spreadRadius: 1,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ],
                                  ),
                                  child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      // Inner highlight
                                      if (isCompleted)
                                        Container(
                                          width: 45,
                                          height: 45,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            gradient: LinearGradient(
                                              begin: Alignment.topLeft,
                                              end: Alignment.center,
                                              colors: [
                                                Colors.white.withValues(alpha: 0.3),
                                                Colors.white.withValues(alpha: 0.0),
                                              ],
                                            ),
                                          ),
                                        ),
                                      // Icon
                                      Icon(
                                        step['icon'],
                                        color: isCompleted ? Colors.white : Colors.grey,
                                        size: 26,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          gradient: isActive
                              ? LinearGradient(
                                  colors: [
                                    step['color'].withValues(alpha: 0.1),
                                    step['color'].withValues(alpha: 0.05),
                                  ],
                                )
                              : null,
                          border: isActive
                              ? Border.all(
                                  color: step['color'].withValues(alpha: 0.3),
                                  width: 1,
                                )
                              : null,
                        ),
                        child: Text(
                          step['title'],
                          textAlign: TextAlign.center,
                          style: GoogleFonts.cairo(
                            fontSize: 10,
                            fontWeight: isActive ? FontWeight.bold : FontWeight.w600,
                            color: isCompleted
                                ? (isActive ? step['color'] : textColor)
                                : Colors.grey,
                            shadows: isActive
                                ? [
                                    Shadow(
                                      color: step['color'].withValues(alpha: 0.3),
                                      blurRadius: 4,
                                      offset: const Offset(0, 1),
                                    ),
                                  ]
                                : null,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentStatusDetails(int currentStepIndex, Color textColor) {
    if (currentStepIndex < 0 || currentStepIndex >= _orderSteps.length) return Container();

    final currentStep = _orderSteps[currentStepIndex];

    return AnimatedBuilder(
      animation: _slideAnimation,
      builder: (context, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: Container(
            padding: const EdgeInsets.all(25),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  currentStep['color'].withValues(alpha: 0.15),
                  currentStep['color'].withValues(alpha: 0.08),
                  currentStep['color'].withValues(alpha: 0.03),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: currentStep['color'].withValues(alpha: 0.4),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: currentStep['color'].withValues(alpha: 0.2),
                  blurRadius: 15,
                  spreadRadius: 3,
                  offset: const Offset(0, 5),
                ),
                BoxShadow(
                  color: widget.isDark
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 20,
                  spreadRadius: 5,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Row(
              children: [
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Container(
                        padding: const EdgeInsets.all(15),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              currentStep['color'],
                              currentStep['color'].withValues(alpha: 0.8),
                              currentStep['color'].withValues(alpha: 0.9),
                            ],
                          ),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: currentStep['color'].withValues(alpha: 0.4),
                              blurRadius: 15,
                              spreadRadius: 3,
                              offset: const Offset(0, 3),
                            ),
                            BoxShadow(
                              color: currentStep['color'].withValues(alpha: 0.2),
                              blurRadius: 25,
                              spreadRadius: 8,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            // Inner highlight
                            Container(
                              width: 35,
                              height: 35,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.center,
                                  colors: [
                                    Colors.white.withValues(alpha: 0.3),
                                    Colors.white.withValues(alpha: 0.0),
                                  ],
                                ),
                              ),
                            ),
                            Icon(
                              currentStep['icon'],
                              color: Colors.white,
                              size: 28,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(width: 15),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        currentStep['title'],
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: textColor,
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        currentStep['subtitle'],
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: textColor.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                if (_currentStatus == 'out_for_delivery')
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: const Color(0xFF27AE60),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '$_estimatedTime دقيقة',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDriverInfo(Color textColor) {
    return AnimatedBuilder(
      animation: _floatingAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _floatingAnimation.value),
          child: Container(
            padding: const EdgeInsets.all(25),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: widget.isDark
                    ? [const Color(0xFF1A1A1A), const Color(0xFF2A2A2A)]
                    : [Colors.white, const Color(0xFFF8F9FA)],
              ),
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: widget.isDark
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 20,
                  spreadRadius: 5,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(15),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF3498DB), Color(0xFF5DADE2)],
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF3498DB).withValues(alpha: 0.3),
                            blurRadius: 15,
                            spreadRadius: 3,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.person,
                        color: Colors.white,
                        size: 25,
                      ),
                    ),
                    const SizedBox(width: 15),
                    Text(
                      'معلومات السائق',
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: textColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: const Color(0xFF3498DB).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: const Color(0xFF3498DB).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.person_outline,
                            color: const Color(0xFF3498DB),
                            size: 20,
                          ),
                          const SizedBox(width: 10),
                          Text(
                            'الاسم: ${_getDriverName()}',
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: textColor,

                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 15),
                      Row(
                        children: [
                          Icon(
                            Icons.phone,
                            color: const Color(0xFF3498DB),
                            size: 20,
                          ),
                          const SizedBox(width: 10),
                          Text(
                            'الهاتف: ${_getDriverPhone()}',
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: textColor,

                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 15),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          _callDriver();
                        },
                        icon: const Icon(Icons.call, size: 18),
                        label: const Text('اتصال'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF27AE60),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 15),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          _messageDriver();
                        },
                        icon: const Icon(Icons.message, size: 18),
                        label: const Text('رسالة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF3498DB),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildOrderDetails(Color textColor) {
    return Container(
      padding: const EdgeInsets.all(25),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: widget.isDark
              ? [const Color(0xFF1A1A1A), const Color(0xFF2A2A2A)]
              : [Colors.white, const Color(0xFFF8F9FA)],
        ),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: widget.isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.1),
            blurRadius: 20,
            spreadRadius: 5,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(15),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF9B59B6), Color(0xFFBB8FCE)],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF9B59B6).withValues(alpha: 0.3),
                      blurRadius: 15,
                      spreadRadius: 3,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.medical_services,
                  color: Colors.white,
                  size: 25,
                ),
              ),
              const SizedBox(width: 15),
              Text(
                'تفاصيل الطلب',
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                  
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFF9B59B6).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: const Color(0xFF9B59B6).withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الأدوية المطلوبة:',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: textColor,
                    
                  ),
                ),
                const SizedBox(height: 15),
                ..._buildMedicinesList(textColor),
                const SizedBox(height: 15),
                Divider(color: textColor.withValues(alpha: 0.2)),
                const SizedBox(height: 15),
                // معلومات إضافية عن الطلب
                _buildOrderInfo(textColor),
                const SizedBox(height: 15),
                Divider(color: textColor.withValues(alpha: 0.2)),
                const SizedBox(height: 15),
                _buildPricingDetails(textColor),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveredButton() {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: ElevatedButton(
              onPressed: () {
                _showDeliveryConfirmationDialog();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF27AE60),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 18),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                elevation: 10,
                shadowColor: const Color(0xFF27AE60).withValues(alpha: 0.3),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.done_all, size: 24),
                  const SizedBox(width: 10),
                  Text(
                    'تأكيد الاستلام',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _showDeliveryConfirmationDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            'تأكيد الاستلام',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'هل تؤكد استلام الطلب؟',
            style: GoogleFonts.cairo(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(
                  color: Colors.grey,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _confirmDelivery();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF27AE60),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: Text(
                'تأكيد',
                style: GoogleFonts.cairo(),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _confirmDelivery() async {
    if (widget.orderId == null) {
      setState(() {
        _currentStatus = 'delivered';
      });
      _showSuccessMessage('تم تأكيد استلام الطلب بنجاح');
      return;
    }

    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          backgroundColor: widget.isDark ? const Color(0xFF1E1E1E) : Colors.white,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(color: Color(0xFF00BF63)),
              const SizedBox(height: 20),
              Text(
                'جاري تأكيد استلام الطلب...',
                style: GoogleFonts.cairo(
                  color: widget.isDark ? Colors.white : const Color(0xFF121212),
                ),
              ),
            ],
          ),
        ),
      );

      // تأكيد استلام الطلب من العميل
      final result = await OrderService.confirmOrder(
        orderId: widget.orderId!,
        action: 'delivered', // تأكيد الاستلام
      );

      if (!mounted) return;

      Navigator.pop(context); // إغلاق مؤشر التحميل

      if (result['success'] == true) {
        setState(() {
          _currentStatus = 'delivered';
          if (_currentOrder != null) {
            _currentOrder!['status'] = 'delivered';
          }
        });

        // عرض رسالة النجاح
        _showSuccessMessage('تم تأكيد استلام الطلب بنجاح');

        // التوجيه إلى الصفحة الرئيسية بعد ثانيتين
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            // العودة إلى الصفحة الرئيسية وإزالة جميع الصفحات السابقة
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                builder: (context) => HomeScreen(
                  isDark: widget.isDark,
                  onToggleDarkMode: widget.onToggleDarkMode,
                  onLogout: () {
                    // يمكن إضافة منطق تسجيل الخروج هنا إذا لزم الأمر
                  },
                ),
              ),
              (route) => false,
            );
          }
        });
      } else {
        _showErrorMessage(result['message'] ?? 'فشل في تأكيد استلام الطلب');
      }
    } catch (e) {
      if (!mounted) return;
      Navigator.pop(context); // إغلاق مؤشر التحميل
      _showErrorMessage('خطأ في الاتصال بالخادم');
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: const Color(0xFF27AE60),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  /// الاتصال بالسائق
  void _callDriver() {
    final driverPhone = _getDriverPhone();
    if (driverPhone.isNotEmpty && driverPhone != 'غير متوفر') {
      // إزالة المسافات والرموز الإضافية من رقم الهاتف
      final cleanPhone = driverPhone.replaceAll(RegExp(r'[^\d+]'), '');

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: widget.isDark ? const Color(0xFF1E1E1E) : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            'الاتصال بالسائق',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              color: widget.isDark ? Colors.white : const Color(0xFF121212),
            ),
          ),
          content: Text(
            'هل تريد الاتصال بالسائق ${_getDriverName()}؟\nرقم الهاتف: $driverPhone',
            style: GoogleFonts.cairo(
              color: widget.isDark ? Colors.white70 : const Color(0xFF666666),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(color: Colors.grey),
              ),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context);
                // هنا يمكن إضافة وظيفة الاتصال الفعلية
                // مثل: launch('tel:$cleanPhone');
                _showSuccessMessage('سيتم فتح تطبيق الهاتف للاتصال بـ $cleanPhone');
              },
              icon: const Icon(Icons.call, size: 18),
              label: Text(
                'اتصال',
                style: GoogleFonts.cairo(),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF27AE60),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      _showErrorMessage('رقم هاتف السائق غير متوفر');
    }
  }

  /// إرسال رسالة للسائق
  void _messageDriver() {
    final driverPhone = _getDriverPhone();
    final driverName = _getDriverName();

    if (driverPhone.isNotEmpty && driverPhone != 'غير متوفر') {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: widget.isDark ? const Color(0xFF1E1E1E) : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            'رسالة للسائق',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              color: widget.isDark ? Colors.white : const Color(0xFF121212),
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'السائق: $driverName',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.w600,
                  color: widget.isDark ? Colors.white : const Color(0xFF121212),
                ),
              ),
              const SizedBox(height: 10),
              Text(
                'اختر رسالة سريعة أو اكتب رسالة مخصصة:',
                style: GoogleFonts.cairo(
                  color: widget.isDark ? Colors.white70 : const Color(0xFF666666),
                ),
              ),
              const SizedBox(height: 15),
              // رسائل سريعة
              _buildQuickMessageButton('أين موقعك الحالي؟'),
              _buildQuickMessageButton('كم الوقت المتبقي للوصول؟'),
              _buildQuickMessageButton('هل يمكنك التواصل معي؟'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(color: Colors.grey),
              ),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context);
                _showCustomMessageDialog(driverName, driverPhone);
              },
              icon: const Icon(Icons.edit, size: 18),
              label: Text(
                'رسالة مخصصة',
                style: GoogleFonts.cairo(),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF3498DB),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      _showErrorMessage('معلومات السائق غير متوفرة');
    }
  }

  /// بناء زر رسالة سريعة
  Widget _buildQuickMessageButton(String message) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: SizedBox(
        width: double.infinity,
        child: OutlinedButton(
          onPressed: () {
            Navigator.pop(context);
            _sendQuickMessage(message);
          },
          style: OutlinedButton.styleFrom(
            side: BorderSide(
              color: widget.isDark ? Colors.white30 : Colors.grey.shade300,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            message,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: widget.isDark ? Colors.white70 : const Color(0xFF666666),
            ),
          ),
        ),
      ),
    );
  }

  /// إرسال رسالة سريعة
  void _sendQuickMessage(String message) {
    // هنا يمكن إضافة وظيفة إرسال الرسالة الفعلية
    // مثل: launch('sms:${_getDriverPhone()}?body=$message');
    _showSuccessMessage('تم إرسال الرسالة: "$message"');
  }

  /// عرض نافذة رسالة مخصصة
  void _showCustomMessageDialog(String driverName, String driverPhone) {
    final TextEditingController messageController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: widget.isDark ? const Color(0xFF1E1E1E) : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Text(
          'رسالة مخصصة',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: widget.isDark ? Colors.white : const Color(0xFF121212),
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'إلى: $driverName',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.w600,
                color: widget.isDark ? Colors.white : const Color(0xFF121212),
              ),
            ),
            const SizedBox(height: 15),
            TextField(
              controller: messageController,
              maxLines: 3,
              decoration: InputDecoration(
                hintText: 'اكتب رسالتك هنا...',
                hintStyle: GoogleFonts.cairo(
                  color: widget.isDark ? Colors.white30 : Colors.grey,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(
                    color: widget.isDark ? Colors.white30 : Colors.grey.shade300,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(
                    color: widget.isDark ? Colors.white30 : Colors.grey.shade300,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(
                    color: Color(0xFF3498DB),
                  ),
                ),
              ),
              style: GoogleFonts.cairo(
                color: widget.isDark ? Colors.white : const Color(0xFF121212),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(color: Colors.grey),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              if (messageController.text.trim().isNotEmpty) {
                Navigator.pop(context);
                _sendQuickMessage(messageController.text.trim());
              }
            },
            icon: const Icon(Icons.send, size: 18),
            label: Text(
              'إرسال',
              style: GoogleFonts.cairo(),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF3498DB),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
