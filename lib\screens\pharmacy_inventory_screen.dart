import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PharmacyInventoryScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;

  const PharmacyInventoryScreen({
    Key? key,
    required this.isDark,
    required this.onToggleDarkMode,
  }) : super(key: key);

  @override
  _PharmacyInventoryScreenState createState() => _PharmacyInventoryScreenState();
}

class _PharmacyInventoryScreenState extends State<PharmacyInventoryScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'الكل';
  bool _isDarkMode = false;

  final List<String> _categories = [
    'الكل',
    'مسكنات الألم',
    'مضادات حيوية',
    'فيتامينات',
    'أدوية القلب',
    'أدوية السكري',
    'مستحضرات تجميل',
    'منتجات الأطفال'
  ];

  final List<Map<String, dynamic>> _medicines = [
    {
      'name': 'باراسيتامول 500 مجم',
      'category': 'مسكنات الألم',
      'stock': 150,
      'minStock': 50,
      'price': 12.50,
      'expiry': '2025-12-31',
      'supplier': 'شركة الدواء المصرية',
      'barcode': '1234567890123',
      'location': 'رف A-1',
    },
    {
      'name': 'أموكسيسيلين 250 مجم',
      'category': 'مضادات حيوية',
      'stock': 25,
      'minStock': 30,
      'price': 45.00,
      'expiry': '2024-08-15',
      'supplier': 'شركة النهدي الطبية',
      'barcode': '2345678901234',
      'location': 'رف B-2',
    },
    {
      'name': 'فيتامين د 1000 وحدة',
      'category': 'فيتامينات',
      'stock': 80,
      'minStock': 40,
      'price': 28.75,
      'expiry': '2026-03-20',
      'supplier': 'شركة الحكمة الدوائية',
      'barcode': '3456789012345',
      'location': 'رف C-3',
    },
    {
      'name': 'أتورفاستاتين 20 مجم',
      'category': 'أدوية القلب',
      'stock': 60,
      'minStock': 25,
      'price': 85.00,
      'expiry': '2025-06-10',
      'supplier': 'شركة فايزر',
      'barcode': '4567890123456',
      'location': 'رف D-1',
    },
    {
      'name': 'ميتفورمين 500 مجم',
      'category': 'أدوية السكري',
      'stock': 10,
      'minStock': 20,
      'price': 32.25,
      'expiry': '2024-11-30',
      'supplier': 'شركة جلاكسو',
      'barcode': '5678901234567',
      'location': 'رف E-2',
    },
  ];

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDark;
    _loadDarkMode();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    _animationController.forward();
  }

  Future<void> _loadDarkMode() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('pharmacy_dark_mode') ?? widget.isDark;
    });
  }

  Future<void> _toggleDarkMode() async {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('pharmacy_dark_mode', _isDarkMode);
    widget.onToggleDarkMode();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  List<Map<String, dynamic>> get _filteredMedicines {
    return _medicines.where((medicine) {
      final matchesSearch = medicine['name']
          .toString()
          .toLowerCase()
          .contains(_searchQuery.toLowerCase());
      final matchesCategory = _selectedCategory == 'الكل' ||
          medicine['category'] == _selectedCategory;
      return matchesSearch && matchesCategory;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = _isDarkMode ? const Color(0xFF1B1B1B) : Colors.white;
    final textColor = _isDarkMode ? Colors.white : const Color(0xFF1B1B1B);
    const primaryColor = Color(0xFF00BF63);
    final cardColor = _isDarkMode
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFF8F9FA);

    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        backgroundColor: bgColor,
        elevation: 0,
        title: Text(
          'إدارة المخزون',
          style: TextStyle(
            color: textColor,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            fontFamily: 'Tajawal',
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: _isDarkMode ? const Color(0xFF2A2A2A) : const Color(0xFFE8F5E8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: Icon(
                _isDarkMode ? Icons.light_mode : Icons.dark_mode,
                color: primaryColor,
              ),
              onPressed: _toggleDarkMode,
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Search Bar
                Container(
                  decoration: BoxDecoration(
                    color: cardColor,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                    style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
                    decoration: InputDecoration(
                      hintText: 'البحث عن دواء...',
                      hintStyle: TextStyle(
                        color: textColor.withValues(alpha: 0.6),
                        fontFamily: 'Tajawal',
                      ),
                      prefixIcon: Icon(Icons.search, color: primaryColor),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Category Filter
                SizedBox(
                  height: 40,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _categories.length,
                    itemBuilder: (context, index) {
                      final category = _categories[index];
                      final isSelected = category == _selectedCategory;
                      return Container(
                        margin: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(
                            category,
                            style: TextStyle(
                              color: isSelected ? Colors.white : textColor,
                              fontFamily: 'Tajawal',
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _selectedCategory = category;
                            });
                          },
                          backgroundColor: cardColor,
                          selectedColor: primaryColor,
                          checkmarkColor: Colors.white,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          // Statistics Cards
          Container(
            height: 100,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الأدوية',
                    _medicines.length.toString(),
                    Icons.medication,
                    primaryColor,
                    cardColor,
                    textColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'نفدت الكمية',
                    _medicines.where((m) => m['stock'] <= m['minStock']).length.toString(),
                    Icons.warning,
                    Colors.orange,
                    cardColor,
                    textColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'تنتهي قريباً',
                    _medicines.where((m) => _isExpiringSoon(m['expiry'])).length.toString(),
                    Icons.schedule,
                    Colors.red,
                    cardColor,
                    textColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // Medicines List
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _filteredMedicines.length,
            itemBuilder: (context, index) {
              final medicine = _filteredMedicines[index];
              return _buildMedicineCard(medicine, cardColor, textColor, primaryColor);
            },
          ),
        ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddMedicineDialog(cardColor, textColor, primaryColor),
        backgroundColor: primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, Color cardColor, Color textColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: textColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal',
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: textColor.withValues(alpha: 0.7),
              fontSize: 10,
              fontFamily: 'Tajawal',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMedicineCard(Map<String, dynamic> medicine, Color cardColor, Color textColor, Color primaryColor) {
    final isLowStock = medicine['stock'] <= medicine['minStock'];
    final isExpiringSoon = _isExpiringSoon(medicine['expiry']);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isLowStock ? Colors.orange : (isExpiringSoon ? Colors.red : Colors.transparent),
          width: isLowStock || isExpiringSoon ? 2 : 0,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  medicine['name'],
                  style: TextStyle(
                    color: textColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Tajawal',
                  ),
                ),
              ),
              if (isLowStock)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'كمية قليلة',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontFamily: 'Tajawal',
                    ),
                  ),
                ),
              if (isExpiringSoon)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'ينتهي قريباً',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontFamily: 'Tajawal',
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            medicine['category'],
            style: TextStyle(
              color: primaryColor,
              fontSize: 12,
              fontFamily: 'Tajawal',
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildInfoRow('الكمية', '${medicine['stock']} قطعة', textColor),
              ),
              Expanded(
                child: _buildInfoRow('السعر', '${medicine['price']} ر.س', textColor),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: _buildInfoRow('الموقع', medicine['location'], textColor),
              ),
              Expanded(
                child: _buildInfoRow('انتهاء الصلاحية', medicine['expiry'], textColor),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _showEditMedicineDialog(medicine, cardColor, textColor, primaryColor),
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('تعديل', style: TextStyle(fontFamily: 'Tajawal')),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _showStockDialog(medicine, cardColor, textColor, primaryColor),
                  icon: const Icon(Icons.add_box, size: 16),
                  label: const Text('إضافة كمية', style: TextStyle(fontFamily: 'Tajawal')),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: primaryColor,
                    side: BorderSide(color: primaryColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, Color textColor) {
    return Row(
      children: [
        Text(
          '$label: ',
          style: TextStyle(
            color: textColor.withValues(alpha: 0.7),
            fontSize: 12,
            fontFamily: 'Tajawal',
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: textColor,
            fontSize: 12,
            fontWeight: FontWeight.bold,
            fontFamily: 'Tajawal',
          ),
        ),
      ],
    );
  }

  bool _isExpiringSoon(String expiryDate) {
    try {
      final expiry = DateTime.parse(expiryDate);
      final now = DateTime.now();
      final difference = expiry.difference(now).inDays;
      return difference <= 90; // expires within 3 months
    } catch (e) {
      return false;
    }
  }

  void _showAddMedicineDialog(Color cardColor, Color textColor, Color primaryColor) {
    // Implementation for add medicine dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: cardColor,
        title: Text(
          'إضافة دواء جديد',
          style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
        ),
        content: const Text(
          'سيتم إضافة هذه الميزة قريباً',
          style: TextStyle(fontFamily: 'Tajawal'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: TextStyle(color: primaryColor, fontFamily: 'Tajawal'),
            ),
          ),
        ],
      ),
    );
  }

  void _showEditMedicineDialog(Map<String, dynamic> medicine, Color cardColor, Color textColor, Color primaryColor) {
    // Implementation for edit medicine dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: cardColor,
        title: Text(
          'تعديل ${medicine['name']}',
          style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
        ),
        content: const Text(
          'سيتم إضافة هذه الميزة قريباً',
          style: TextStyle(fontFamily: 'Tajawal'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: TextStyle(color: primaryColor, fontFamily: 'Tajawal'),
            ),
          ),
        ],
      ),
    );
  }

  void _showStockDialog(Map<String, dynamic> medicine, Color cardColor, Color textColor, Color primaryColor) {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: cardColor,
        title: Text(
          'إضافة كمية - ${medicine['name']}',
          style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'الكمية الحالية: ${medicine['stock']} قطعة',
              style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
              decoration: InputDecoration(
                labelText: 'الكمية المضافة',
                labelStyle: TextStyle(color: textColor.withValues(alpha: 0.7), fontFamily: 'Tajawal'),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: primaryColor),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: primaryColor),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(color: textColor.withValues(alpha: 0.7), fontFamily: 'Tajawal'),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              final addedQuantity = int.tryParse(controller.text) ?? 0;
              if (addedQuantity > 0) {
                setState(() {
                  medicine['stock'] += addedQuantity;
                });
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'تم إضافة $addedQuantity قطعة بنجاح',
                      style: const TextStyle(fontFamily: 'Tajawal'),
                    ),
                    backgroundColor: primaryColor,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'إضافة',
              style: TextStyle(fontFamily: 'Tajawal'),
            ),
          ),
        ],
      ),
    );
  }
}
