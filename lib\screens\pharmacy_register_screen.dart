import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'map_picker_screen.dart';
import 'pharmacy_login_screen.dart';
import '../services/pharmacy_service.dart';

const Color kInDriveGreen = Color(0xFF00BF63);
const Color kDarkGrey = Color(0xFF1B1B1B);

class PharmacyRegisterScreen extends StatefulWidget {
  final bool isDark;
  const PharmacyRegisterScreen({super.key, this.isDark = false});

  @override
  State<PharmacyRegisterScreen> createState() => _PharmacyRegisterScreenState();
}

class _PharmacyRegisterScreenState extends State<PharmacyRegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _pharmacyNameController = TextEditingController();
  final _ownerNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _licenseNumberController = TextEditingController();
  final _ownerNationalIdController = TextEditingController();

  String? _selectedGovernorate;
  LatLng? _pharmacyLocation;
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  static const List<String> _governorates = [
    'القاهرة',
    'الجيزة',
    'الإسكندرية',
    'الدقهلية',
    'البحيرة',
    'الشرقية',
    'المنوفية',
    'القليوبية',
    'الغربية',
    'سوهاج',
    'أسيوط',
    'المنيا',
    'قنا',
    'الفيوم',
    'بني سويف',
    'دمياط',
    'الإسماعيلية',
    'أسوان',
    'الأقصر',
    'بورسعيد',
    'السويس',
    'مطروح',
    'شمال سيناء',
    'جنوب سيناء',
    'البحر الأحمر',
    'الوادي الجديد',
  ];

  /// وظيفة تسجيل الصيدلية
  Future<void> _registerPharmacy() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedGovernorate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار المحافظة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await PharmacyService.registerPharmacy(
        name: _pharmacyNameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
        password: _passwordController.text,
        licenseNumber: _licenseNumberController.text.trim(),
        address: _addressController.text.trim(),
        city: _cityController.text.trim(),
        governorate: _selectedGovernorate!,
        ownerName: _ownerNameController.text.trim(),
        ownerNationalId: _ownerNationalIdController.text.trim(),
        coordinates: _pharmacyLocation != null
            ? {
                'latitude': _pharmacyLocation!.latitude,
                'longitude': _pharmacyLocation!.longitude,
              }
            : {
                'latitude': 30.0444, // إحداثيات افتراضية للقاهرة
                'longitude': 31.2357,
              },
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (result['success'] == true) {
          // إظهار رسالة نجاح
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                result['message'] ?? 'تم تسجيل الصيدلية بنجاح',
                style: const TextStyle(fontFamily: 'Tajawal'),
              ),
              backgroundColor: Colors.green,
            ),
          );

          // العودة إلى شاشة تسجيل الدخول
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (_) => PharmacyLoginScreen(
                isDark: widget.isDark,
                onToggleDarkMode: () {},
              ),
            ),
          );
        } else {
          // إظهار رسالة خطأ
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                result['message'] ?? 'فشل في تسجيل الصيدلية',
                style: const TextStyle(fontFamily: 'Tajawal'),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ: ${e.toString()}',
              style: const TextStyle(fontFamily: 'Tajawal'),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = widget.isDark ? kDarkGrey : Colors.white;
    final textColor = widget.isDark ? Colors.white : kDarkGrey;

    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        backgroundColor: bgColor,
        elevation: 0,
        iconTheme: IconThemeData(color: kInDriveGreen),
        title: Text(
          'إنشاء حساب صيدلية',
          style: TextStyle(
            color: textColor,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Center(
        child: SingleChildScrollView(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: widget.isDark ? kDarkGrey : Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: kInDriveGreen.withValues(alpha: 0.07),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  Icon(Icons.local_pharmacy, size: 60, color: kInDriveGreen),
                  const SizedBox(height: 16),
                  Text(
                    'إنشاء حساب صيدلية',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: textColor,
                      fontFamily: 'Poppins',
                    ),
                  ),
                  const SizedBox(height: 24),
                  _buildTextField(
                    _pharmacyNameController,
                    'اسم الصيدلية',
                    Icons.local_pharmacy,
                    textColor,
                  ),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _ownerNameController,
                    'اسم صاحب الصيدلية',
                    Icons.person,
                    textColor,
                  ),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _emailController,
                    'البريد الإلكتروني',
                    Icons.email,
                    textColor,
                  ),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _phoneController,
                    'رقم الهاتف',
                    Icons.phone,
                    textColor,
                  ),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _addressController,
                    'العنوان التفصيلي (الشارع)',
                    Icons.location_on,
                    textColor,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال عنوان الشارع';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _cityController,
                    'المدينة',
                    Icons.location_city,
                    textColor,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال المدينة';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 12),
                  DropdownButtonFormField<String>(
                    value: _selectedGovernorate,
                    items: _governorates
                        .map(
                          (g) => DropdownMenuItem(
                            value: g,
                            child: Text(
                              g,
                              style: TextStyle(
                                color: textColor,
                                fontFamily: 'Poppins',
                              ),
                            ),
                          ),
                        )
                        .toList(),
                    onChanged: (v) => setState(() => _selectedGovernorate = v),
                    decoration: InputDecoration(
                      labelText: 'المحافظة',
                      labelStyle: TextStyle(color: textColor),
                      prefixIcon: Icon(Icons.map, color: kInDriveGreen),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      filled: true,
                      fillColor: widget.isDark ? kDarkGrey : Colors.grey[50],
                    ),
                    validator: (v) => v == null ? 'اختر المحافظة' : null,
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[600],
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        elevation: 2,
                        textStyle: const TextStyle(fontFamily: 'Poppins'),
                      ),
                      onPressed: () async {
                        final result = await Navigator.push<LatLng>(
                          context,
                          MaterialPageRoute(
                            builder: (context) => MapPickerScreen(
                              initialLocation: _pharmacyLocation,
                            ),
                          ),
                        );
                        if (result != null) {
                          setState(() {
                            _pharmacyLocation = result;
                          });
                        }
                      },
                      icon: const Icon(Icons.map),
                      label: Text(
                        _pharmacyLocation == null
                            ? 'تحديد الموقع على الخريطة'
                            : 'تم اختيار الموقع (${_pharmacyLocation!.latitude.toStringAsFixed(4)}, ${_pharmacyLocation!.longitude.toStringAsFixed(4)})',
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _passwordController,
                    'كلمة المرور',
                    Icons.lock,
                    textColor,
                    isPassword: true,
                  ),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _confirmPasswordController,
                    'تأكيد كلمة المرور',
                    Icons.lock_outline,
                    textColor,
                    isPassword: true,
                  ),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _licenseNumberController,
                    'رقم ترخيص الصيدلية',
                    Icons.assignment,
                    textColor,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال رقم ترخيص الصيدلية';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _ownerNationalIdController,
                    'الرقم القومي للمالك',
                    Icons.credit_card,
                    textColor,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال الرقم القومي للمالك';
                      }
                      if (value.length != 14) {
                        return 'الرقم القومي يجب أن يكون 14 رقم';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kInDriveGreen,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        textStyle: const TextStyle(
                          fontSize: 18,
                          fontFamily: 'Poppins',
                        ),
                        elevation: 4,
                      ),
                      onPressed: _isLoading ? null : _registerPharmacy,
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Text('إنشاء الحساب'),
                    ),
                  ),
                  const SizedBox(height: 12),
                  TextButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => PharmacyLoginScreen(
                            isDark: widget.isDark,
                            onToggleDarkMode: () {
                              setState(() {});
                            },
                          ),
                        ),
                      );
                    },
                    child: Text(
                      'لديك حساب؟ تسجيل الدخول',
                      style: TextStyle(
                        color: kInDriveGreen,
                        fontFamily: 'Poppins',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    String label,
    IconData icon,
    Color textColor, {
    bool isPassword = false,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: isPassword,
      style: TextStyle(color: textColor, fontFamily: 'Poppins'),
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(color: textColor),
        prefixIcon: Icon(icon, color: kInDriveGreen),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(30)),
        filled: true,
        fillColor: widget.isDark ? kDarkGrey : Colors.grey[50],
      ),
    );
  }

  @override
  void dispose() {
    _pharmacyNameController.dispose();
    _ownerNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _licenseNumberController.dispose();
    _ownerNationalIdController.dispose();
    super.dispose();
  }
}
