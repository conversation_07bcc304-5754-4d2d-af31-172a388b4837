import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:math';

class PharmacyReportsScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;

  const PharmacyReportsScreen({
    Key? key,
    required this.isDark,
    required this.onToggleDarkMode,
  }) : super(key: key);

  @override
  _PharmacyReportsScreenState createState() => _PharmacyReportsScreenState();
}

class _PharmacyReportsScreenState extends State<PharmacyReportsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  String _selectedPeriod = 'اليوم';
  bool _isDarkMode = false;

  final List<String> _periods = ['اليوم', 'الأسبوع', 'الشهر', 'السنة'];

  // Sample data for reports
  final Map<String, Map<String, dynamic>> _reportData = {
    'اليوم': {
      'sales': 2450.75,
      'orders': 18,
      'customers': 15,
      'profit': 735.25,
      'topMedicines': [
        {'name': 'باراسيتامول', 'quantity': 25, 'revenue': 312.50},
        {'name': 'أموكسيسيلين', 'quantity': 8, 'revenue': 360.00},
        {'name': 'فيتامين د', 'quantity': 12, 'revenue': 345.00},
      ],
      'hourlyData': [
        {'hour': '9:00', 'sales': 150.0},
        {'hour': '10:00', 'sales': 280.0},
        {'hour': '11:00', 'sales': 320.0},
        {'hour': '12:00', 'sales': 450.0},
        {'hour': '13:00', 'sales': 380.0},
        {'hour': '14:00', 'sales': 290.0},
        {'hour': '15:00', 'sales': 340.0},
        {'hour': '16:00', 'sales': 240.0},
      ],
    },
    'الأسبوع': {
      'sales': 15680.50,
      'orders': 124,
      'customers': 89,
      'profit': 4704.15,
      'topMedicines': [
        {'name': 'باراسيتامول', 'quantity': 180, 'revenue': 2250.00},
        {'name': 'أموكسيسيلين', 'quantity': 65, 'revenue': 2925.00},
        {'name': 'فيتامين د', 'quantity': 95, 'revenue': 2731.25},
      ],
      'dailyData': [
        {'day': 'السبت', 'sales': 2100.0},
        {'day': 'الأحد', 'sales': 2450.0},
        {'day': 'الاثنين', 'sales': 2280.0},
        {'day': 'الثلاثاء', 'sales': 2650.0},
        {'day': 'الأربعاء', 'sales': 2380.0},
        {'day': 'الخميس', 'sales': 1920.0},
        {'day': 'الجمعة', 'sales': 1900.0},
      ],
    },
  };

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDark;
    _loadDarkMode();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    _animationController.forward();
  }

  Future<void> _loadDarkMode() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('pharmacy_dark_mode') ?? widget.isDark;
    });
  }

  Future<void> _toggleDarkMode() async {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('pharmacy_dark_mode', _isDarkMode);
    widget.onToggleDarkMode();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = _isDarkMode ? const Color(0xFF1B1B1B) : Colors.white;
    final textColor = _isDarkMode ? Colors.white : const Color(0xFF1B1B1B);
    const primaryColor = Color(0xFF00BF63);
    final cardColor = _isDarkMode
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFF8F9FA);

    final currentData = _reportData[_selectedPeriod] ?? _reportData['اليوم']!;

    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        backgroundColor: bgColor,
        elevation: 0,
        title: Text(
          'التقارير والإحصائيات',
          style: TextStyle(
            color: textColor,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            fontFamily: 'Tajawal',
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: _isDarkMode ? const Color(0xFF2A2A2A) : const Color(0xFFE8F5E8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: Icon(
                _isDarkMode ? Icons.light_mode : Icons.dark_mode,
                color: primaryColor,
              ),
              onPressed: _toggleDarkMode,
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Period Selector
            Container(
              height: 50,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _periods.length,
                itemBuilder: (context, index) {
                  final period = _periods[index];
                  final isSelected = period == _selectedPeriod;
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(
                        period,
                        style: TextStyle(
                          color: isSelected ? Colors.white : textColor,
                          fontFamily: 'Tajawal',
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          _selectedPeriod = period;
                        });
                      },
                      backgroundColor: cardColor,
                      selectedColor: primaryColor,
                      checkmarkColor: Colors.white,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 20),

            // Main Statistics Cards
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.2,
              children: [
                _buildStatCard(
                  'إجمالي المبيعات',
                  '${currentData['sales']?.toStringAsFixed(2)} ر.س',
                  Icons.attach_money,
                  primaryColor,
                  cardColor,
                  textColor,
                ),
                _buildStatCard(
                  'عدد الطلبات',
                  '${currentData['orders']}',
                  Icons.shopping_cart,
                  Colors.blue,
                  cardColor,
                  textColor,
                ),
                _buildStatCard(
                  'العملاء',
                  '${currentData['customers']}',
                  Icons.people,
                  Colors.orange,
                  cardColor,
                  textColor,
                ),
                _buildStatCard(
                  'صافي الربح',
                  '${currentData['profit']?.toStringAsFixed(2)} ر.س',
                  Icons.trending_up,
                  Colors.green,
                  cardColor,
                  textColor,
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Sales Chart
            _buildSalesChart(currentData, cardColor, textColor, primaryColor),
            const SizedBox(height: 24),

            // Top Medicines
            _buildTopMedicines(currentData, cardColor, textColor, primaryColor),
            const SizedBox(height: 24),

            // Quick Actions
            _buildQuickActions(cardColor, textColor, primaryColor),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, Color cardColor, Color textColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: textColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              color: textColor.withValues(alpha: 0.7),
              fontSize: 12,
              fontFamily: 'Tajawal',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSalesChart(Map<String, dynamic> data, Color cardColor, Color textColor, Color primaryColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مخطط المبيعات',
            style: TextStyle(
              color: textColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal',
            ),
          ),
          const SizedBox(height: 16),
          Container(
            height: 200,
            child: _buildSimpleChart(data, primaryColor, textColor),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleChart(Map<String, dynamic> data, Color primaryColor, Color textColor) {
    final chartData = _selectedPeriod == 'اليوم' 
        ? data['hourlyData'] as List<Map<String, dynamic>>?
        : data['dailyData'] as List<Map<String, dynamic>>?;
    
    if (chartData == null || chartData.isEmpty) {
      return Center(
        child: Text(
          'لا توجد بيانات للعرض',
          style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
        ),
      );
    }

    final maxValue = chartData.map((e) => e['sales'] as double).reduce(max);
    
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: chartData.map((item) {
        final value = item['sales'] as double;
        final height = (value / maxValue) * 150;
        final label = _selectedPeriod == 'اليوم' ? item['hour'] : item['day'];
        
        return Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  height: height,
                  decoration: BoxDecoration(
                    color: primaryColor,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(4)),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  label.toString(),
                  style: TextStyle(
                    color: textColor.withValues(alpha: 0.7),
                    fontSize: 10,
                    fontFamily: 'Tajawal',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTopMedicines(Map<String, dynamic> data, Color cardColor, Color textColor, Color primaryColor) {
    final topMedicines = data['topMedicines'] as List<Map<String, dynamic>>?;
    
    if (topMedicines == null || topMedicines.isEmpty) {
      return Container();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الأدوية الأكثر مبيعاً',
            style: TextStyle(
              color: textColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal',
            ),
          ),
          const SizedBox(height: 16),
          ...topMedicines.map((medicine) => Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _isDarkMode ? const Color(0xFF3A3A3A) : Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: primaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.medication, color: primaryColor),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        medicine['name'],
                        style: TextStyle(
                          color: textColor,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                      Text(
                        'الكمية: ${medicine['quantity']}',
                        style: TextStyle(
                          color: textColor.withValues(alpha: 0.7),
                          fontSize: 12,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '${medicine['revenue']} ر.س',
                  style: TextStyle(
                    color: primaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Tajawal',
                  ),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }

  Widget _buildQuickActions(Color cardColor, Color textColor, Color primaryColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجراءات سريعة',
            style: TextStyle(
              color: textColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal',
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _exportReport(),
                  icon: const Icon(Icons.download, size: 16),
                  label: const Text('تصدير التقرير', style: TextStyle(fontFamily: 'Tajawal')),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _printReport(),
                  icon: const Icon(Icons.print, size: 16),
                  label: const Text('طباعة', style: TextStyle(fontFamily: 'Tajawal')),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: primaryColor,
                    side: BorderSide(color: primaryColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة التصدير قريباً', style: TextStyle(fontFamily: 'Tajawal')),
        backgroundColor: Color(0xFF00BF63),
      ),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة الطباعة قريباً', style: TextStyle(fontFamily: 'Tajawal')),
        backgroundColor: Color(0xFF00BF63),
      ),
    );
  }
}
