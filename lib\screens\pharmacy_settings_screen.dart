import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PharmacySettingsScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;
  final VoidCallback onLogout;

  const PharmacySettingsScreen({
    Key? key,
    required this.isDark,
    required this.onToggleDarkMode,
    required this.onLogout,
  }) : super(key: key);

  @override
  _PharmacySettingsScreenState createState() => _PharmacySettingsScreenState();
}

class _PharmacySettingsScreenState extends State<PharmacySettingsScreen> {
  bool _isDarkMode = false;
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _autoBackup = true;
  String _language = 'العربية';

  final Map<String, String> _pharmacyInfo = {
    'name': 'صيدلية النور',
    'license': 'PH-2024-001',
    'phone': '+966 50 123 4567',
    'email': '<EMAIL>',
    'address': 'شارع الملك فهد، الرياض، المملكة العربية السعودية',
    'workingHours': '8:00 ص - 12:00 م',
  };

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDark;
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('pharmacy_dark_mode') ?? widget.isDark;
      _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
      _soundEnabled = prefs.getBool('sound_enabled') ?? true;
      _autoBackup = prefs.getBool('auto_backup') ?? true;
      _language = prefs.getString('language') ?? 'العربية';
    });
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('pharmacy_dark_mode', _isDarkMode);
    await prefs.setBool('notifications_enabled', _notificationsEnabled);
    await prefs.setBool('sound_enabled', _soundEnabled);
    await prefs.setBool('auto_backup', _autoBackup);
    await prefs.setString('language', _language);
  }

  Future<void> _toggleDarkMode() async {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
    await _saveSettings();
    widget.onToggleDarkMode();
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = _isDarkMode ? const Color(0xFF1B1B1B) : Colors.white;
    final textColor = _isDarkMode ? Colors.white : const Color(0xFF1B1B1B);
    const primaryColor = Color(0xFF00BF63);
    final cardColor = _isDarkMode
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFF8F9FA);

    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        backgroundColor: bgColor,
        elevation: 0,
        title: Text(
          'الإعدادات',
          style: TextStyle(
            color: textColor,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            fontFamily: 'Tajawal',
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Pharmacy Information
            _buildSectionTitle('معلومات الصيدلية', textColor),
            _buildPharmacyInfoCard(cardColor, textColor, primaryColor),
            const SizedBox(height: 24),

            // App Settings
            _buildSectionTitle('إعدادات التطبيق', textColor),
            _buildAppSettingsCard(cardColor, textColor, primaryColor),
            const SizedBox(height: 24),

            // Notifications Settings
            _buildSectionTitle('إعدادات الإشعارات', textColor),
            _buildNotificationSettingsCard(cardColor, textColor, primaryColor),
            const SizedBox(height: 24),

            // Data & Backup
            _buildSectionTitle('البيانات والنسخ الاحتياطي', textColor),
            _buildDataBackupCard(cardColor, textColor, primaryColor),
            const SizedBox(height: 24),

            // Account Actions
            _buildSectionTitle('إجراءات الحساب', textColor),
            _buildAccountActionsCard(cardColor, textColor, primaryColor),
            const SizedBox(height: 24),

            // About
            _buildSectionTitle('حول التطبيق', textColor),
            _buildAboutCard(cardColor, textColor, primaryColor),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, Color textColor) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: TextStyle(
          color: textColor,
          fontSize: 18,
          fontWeight: FontWeight.bold,
          fontFamily: 'Tajawal',
        ),
      ),
    );
  }

  Widget _buildPharmacyInfoCard(Color cardColor, Color textColor, Color primaryColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildInfoRow('اسم الصيدلية', _pharmacyInfo['name']!, textColor),
          _buildInfoRow('رقم الترخيص', _pharmacyInfo['license']!, textColor),
          _buildInfoRow('رقم الهاتف', _pharmacyInfo['phone']!, textColor),
          _buildInfoRow('البريد الإلكتروني', _pharmacyInfo['email']!, textColor),
          _buildInfoRow('العنوان', _pharmacyInfo['address']!, textColor),
          _buildInfoRow('ساعات العمل', _pharmacyInfo['workingHours']!, textColor),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _editPharmacyInfo(cardColor, textColor, primaryColor),
              icon: const Icon(Icons.edit, size: 16),
              label: const Text('تعديل المعلومات', style: TextStyle(fontFamily: 'Tajawal')),
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppSettingsCard(Color cardColor, Color textColor, Color primaryColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildSwitchTile(
            'الوضع المظلم',
            'تفعيل المظهر المظلم للتطبيق',
            Icons.dark_mode,
            _isDarkMode,
            (value) => _toggleDarkMode(),
            textColor,
            primaryColor,
          ),
          const Divider(),
          _buildLanguageTile(textColor, primaryColor),
        ],
      ),
    );
  }

  Widget _buildNotificationSettingsCard(Color cardColor, Color textColor, Color primaryColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildSwitchTile(
            'الإشعارات',
            'تلقي إشعارات الطلبات الجديدة',
            Icons.notifications,
            _notificationsEnabled,
            (value) {
              setState(() {
                _notificationsEnabled = value;
              });
              _saveSettings();
            },
            textColor,
            primaryColor,
          ),
          const Divider(),
          _buildSwitchTile(
            'الأصوات',
            'تشغيل الأصوات مع الإشعارات',
            Icons.volume_up,
            _soundEnabled,
            (value) {
              setState(() {
                _soundEnabled = value;
              });
              _saveSettings();
            },
            textColor,
            primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildDataBackupCard(Color cardColor, Color textColor, Color primaryColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildSwitchTile(
            'النسخ الاحتياطي التلقائي',
            'إنشاء نسخة احتياطية يومياً',
            Icons.backup,
            _autoBackup,
            (value) {
              setState(() {
                _autoBackup = value;
              });
              _saveSettings();
            },
            textColor,
            primaryColor,
          ),
          const Divider(),
          _buildActionTile(
            'إنشاء نسخة احتياطية الآن',
            'حفظ البيانات الحالية',
            Icons.cloud_upload,
            () => _createBackup(),
            textColor,
            primaryColor,
          ),
          const Divider(),
          _buildActionTile(
            'استعادة من نسخة احتياطية',
            'استعادة البيانات المحفوظة',
            Icons.cloud_download,
            () => _restoreBackup(),
            textColor,
            primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildAccountActionsCard(Color cardColor, Color textColor, Color primaryColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildActionTile(
            'تغيير كلمة المرور',
            'تحديث كلمة مرور الحساب',
            Icons.lock,
            () => _changePassword(cardColor, textColor, primaryColor),
            textColor,
            primaryColor,
          ),
          const Divider(),
          _buildActionTile(
            'تسجيل الخروج',
            'الخروج من الحساب الحالي',
            Icons.logout,
            () => _confirmLogout(cardColor, textColor, primaryColor),
            textColor,
            Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildAboutCard(Color cardColor, Color textColor, Color primaryColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildInfoRow('إصدار التطبيق', '1.0.0', textColor),
          _buildInfoRow('آخر تحديث', '2024-01-15', textColor),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _showPrivacyPolicy(cardColor, textColor, primaryColor),
                  icon: const Icon(Icons.privacy_tip, size: 16),
                  label: const Text('سياسة الخصوصية', style: TextStyle(fontFamily: 'Tajawal')),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: primaryColor,
                    side: BorderSide(color: primaryColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _contactSupport(cardColor, textColor, primaryColor),
                  icon: const Icon(Icons.support, size: 16),
                  label: const Text('الدعم الفني', style: TextStyle(fontFamily: 'Tajawal')),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: primaryColor,
                    side: BorderSide(color: primaryColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, Color textColor) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                color: textColor.withValues(alpha: 0.7),
                fontSize: 14,
                fontFamily: 'Tajawal',
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: textColor,
                fontSize: 14,
                fontWeight: FontWeight.bold,
                fontFamily: 'Tajawal',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile(String title, String subtitle, IconData icon, bool value, Function(bool) onChanged, Color textColor, Color primaryColor) {
    return ListTile(
      leading: Icon(icon, color: primaryColor),
      title: Text(
        title,
        style: TextStyle(
          color: textColor,
          fontSize: 16,
          fontWeight: FontWeight.bold,
          fontFamily: 'Tajawal',
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: textColor.withValues(alpha: 0.7),
          fontSize: 12,
          fontFamily: 'Tajawal',
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: primaryColor,
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildActionTile(String title, String subtitle, IconData icon, VoidCallback onTap, Color textColor, Color iconColor) {
    return ListTile(
      leading: Icon(icon, color: iconColor),
      title: Text(
        title,
        style: TextStyle(
          color: textColor,
          fontSize: 16,
          fontWeight: FontWeight.bold,
          fontFamily: 'Tajawal',
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: textColor.withValues(alpha: 0.7),
          fontSize: 12,
          fontFamily: 'Tajawal',
        ),
      ),
      trailing: Icon(Icons.arrow_forward_ios, color: textColor.withValues(alpha: 0.5), size: 16),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildLanguageTile(Color textColor, Color primaryColor) {
    return ListTile(
      leading: Icon(Icons.language, color: primaryColor),
      title: Text(
        'اللغة',
        style: TextStyle(
          color: textColor,
          fontSize: 16,
          fontWeight: FontWeight.bold,
          fontFamily: 'Tajawal',
        ),
      ),
      subtitle: Text(
        _language,
        style: TextStyle(
          color: textColor.withValues(alpha: 0.7),
          fontSize: 12,
          fontFamily: 'Tajawal',
        ),
      ),
      trailing: Icon(Icons.arrow_forward_ios, color: textColor.withValues(alpha: 0.5), size: 16),
      onTap: () => _changeLanguage(),
      contentPadding: EdgeInsets.zero,
    );
  }

  void _editPharmacyInfo(Color cardColor, Color textColor, Color primaryColor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: cardColor,
        title: Text(
          'تعديل معلومات الصيدلية',
          style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
        ),
        content: const Text(
          'سيتم إضافة هذه الميزة قريباً',
          style: TextStyle(fontFamily: 'Tajawal'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: TextStyle(color: primaryColor, fontFamily: 'Tajawal'),
            ),
          ),
        ],
      ),
    );
  }

  void _changeLanguage() {
    // Language change implementation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة تغيير اللغة قريباً', style: TextStyle(fontFamily: 'Tajawal')),
        backgroundColor: Color(0xFF00BF63),
      ),
    );
  }

  void _createBackup() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إنشاء النسخة الاحتياطية بنجاح', style: TextStyle(fontFamily: 'Tajawal')),
        backgroundColor: Color(0xFF00BF63),
      ),
    );
  }

  void _restoreBackup() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة الاستعادة قريباً', style: TextStyle(fontFamily: 'Tajawal')),
        backgroundColor: Color(0xFF00BF63),
      ),
    );
  }

  void _changePassword(Color cardColor, Color textColor, Color primaryColor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: cardColor,
        title: Text(
          'تغيير كلمة المرور',
          style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
        ),
        content: const Text(
          'سيتم إضافة هذه الميزة قريباً',
          style: TextStyle(fontFamily: 'Tajawal'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: TextStyle(color: primaryColor, fontFamily: 'Tajawal'),
            ),
          ),
        ],
      ),
    );
  }

  void _confirmLogout(Color cardColor, Color textColor, Color primaryColor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: cardColor,
        title: Text(
          'تأكيد تسجيل الخروج',
          style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
        ),
        content: Text(
          'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
          style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(color: textColor.withValues(alpha: 0.7), fontFamily: 'Tajawal'),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onLogout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'تسجيل الخروج',
              style: TextStyle(fontFamily: 'Tajawal'),
            ),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy(Color cardColor, Color textColor, Color primaryColor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: cardColor,
        title: Text(
          'سياسة الخصوصية',
          style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
        ),
        content: const Text(
          'سيتم إضافة سياسة الخصوصية قريباً',
          style: TextStyle(fontFamily: 'Tajawal'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: TextStyle(color: primaryColor, fontFamily: 'Tajawal'),
            ),
          ),
        ],
      ),
    );
  }

  void _contactSupport(Color cardColor, Color textColor, Color primaryColor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: cardColor,
        title: Text(
          'الدعم الفني',
          style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
        ),
        content: Text(
          'للتواصل مع الدعم الفني:\nالهاتف: +966 50 123 4567\nالبريد الإلكتروني: <EMAIL>',
          style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: TextStyle(color: primaryColor, fontFamily: 'Tajawal'),
            ),
          ),
        ],
      ),
    );
  }
}
