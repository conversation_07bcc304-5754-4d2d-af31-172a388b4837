import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import '../services/user_service.dart';

import 'user_login_screen.dart';
import 'home_screen.dart';

class ProfileTab extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;
  final VoidCallback? onLogout;
  const ProfileTab({
    super.key,
    required this.isDark,
    required this.onToggleDarkMode,
    this.onLogout,
  });

  @override
  State<ProfileTab> createState() => _ProfileTabState();
}

class _ProfileTabState extends State<ProfileTab> {
  File? _profileImage;
  final _formKey = GlobalKey<FormState>();
  bool _isEditing = false;
  bool _isLoading = false;

  // بيانات المستخدم
  String _name = 'اسم المستخدم';
  String _email = '<EMAIL>';
  String _phone = '01000000000';
  String _address = 'القاهرة، مصر';
  String _governorate = '';
  bool _isEmailVerified = false;
  String _createdAt = '';
  String _lastLogin = '';

  // Controllers للتعديل
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _governorateController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadProfileData();
    _loadProfileImage();
  }

  Future<void> _loadProfileData() async {
    try {
      // تحميل البيانات من UserService
      final userName = await UserService.getUserName();
      final userEmail = await UserService.getUserEmail();
      final userPhone = await UserService.getUserPhone();
      final userAddress = await UserService.getUserAddress();
      final userGovernorate = await UserService.getUserGovernorate();
      final isEmailVerified = await UserService.isEmailVerified();
      final createdAt = await UserService.getCreatedAt();
      final lastLogin = await UserService.getLastLogin();

      if (mounted) {
        setState(() {
          _name = userName;
          _email = userEmail;
          _phone = userPhone;
          _address = userAddress;
          _governorate = userGovernorate;
          _isEmailVerified = isEmailVerified;
          _createdAt = createdAt;
          _lastLogin = lastLogin;

          // تحديث Controllers
          _nameController.text = _name;
          _emailController.text = _email;
          _phoneController.text = _phone;
          _addressController.text = _address;
          _governorateController.text = _governorate;
        });
      }
    } catch (e) {
      print('Error loading user data: $e');
      // في حالة الخطأ، استخدم البيانات المحفوظة محلياً
      _loadLocalProfileData();
    }
  }

  Future<void> _loadLocalProfileData() async {
    final prefs = await SharedPreferences.getInstance();
    if (mounted) {
      setState(() {
        _name = prefs.getString('profile_name') ?? 'اسم المستخدم';
        _email = prefs.getString('profile_email') ?? '<EMAIL>';
        _phone = prefs.getString('profile_phone') ?? '01000000000';
        _address = prefs.getString('profile_address') ?? 'القاهرة، مصر';
        _nameController.text = _name;
        _emailController.text = _email;
        _phoneController.text = _phone;
        _addressController.text = _address;
      });
    }
  }

  Future<void> _saveProfileData() async {
    try {
      // تحديث البيانات في UserService
      await UserService.updateUserData({
        'name': _name,
        'email': _email,
        'phone': _phone,
        'address': _address,
        'governorate': _governorate,
      });

      // حفظ محلياً أيضاً
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('profile_name', _name);
      await prefs.setString('profile_email', _email);
      await prefs.setString('profile_phone', _phone);
      await prefs.setString('profile_address', _address);
    } catch (e) {
      print('Error saving profile data: $e');
    }
  }

  Future<void> _loadProfileImage() async {
    final prefs = await SharedPreferences.getInstance();
    final path = prefs.getString('profile_image_path');
    if (path != null && File(path).existsSync()) {
      setState(() {
        _profileImage = File(path);
      });
    }
  }

  Future<void> _saveProfileImagePath(String path) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('profile_image_path', path);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _governorateController.dispose();
    super.dispose();
  }

  Future<void> _logout() async {
    try {
      await UserService.clearUserData();
      if (mounted) {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const UserLoginScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      print('Error during logout: $e');
    }
  }

  Future<void> _deleteAccount() async {
    // عرض تأكيد الحذف
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'حذف الحساب',
          style: TextStyle(fontFamily: 'Poppins'),
        ),
        content: const Text(
          'هل أنت متأكد من رغبتك في حذف الحساب؟ هذا الإجراء لا يمكن التراجع عنه.',
          style: TextStyle(fontFamily: 'Poppins'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text(
              'إلغاء',
              style: TextStyle(fontFamily: 'Poppins'),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text(
              'حذف',
              style: TextStyle(fontFamily: 'Poppins'),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        // TODO: إضافة API call لحذف الحساب من الخادم
        // await ApiService().deleteAccount();

        // حذف البيانات المحلية
        await UserService.clearUserData();
        final prefs = await SharedPreferences.getInstance();
        await prefs.clear();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'تم حذف الحساب بنجاح',
                style: TextStyle(fontFamily: 'Poppins'),
              ),
              backgroundColor: Colors.green,
            ),
          );

          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const UserLoginScreen()),
            (route) => false,
          );
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'خطأ في حذف الحساب: ${e.toString()}',
                style: const TextStyle(fontFamily: 'Poppins'),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final picked = await picker.pickImage(source: ImageSource.gallery);
    if (picked != null) {
      // انسخ الصورة إلى مجلد التطبيق المؤقت
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = 'profile_${DateTime.now().millisecondsSinceEpoch}.png';
      final savedImage = await File(
        picked.path,
      ).copy('${appDir.path}/$fileName');
      await _saveProfileImagePath(savedImage.path);
      await _loadProfileImage(); // أضف هذا السطر لضمان ظهور الصورة فورًا
    }
  }

  void _startEdit() {
    setState(() {
      _isEditing = true;
      _nameController.text = _name;
      _emailController.text = _email;
      _phoneController.text = _phone;
      _addressController.text = _address;
    });
  }

  void _saveEdit() {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _name = _nameController.text;
        _email = _emailController.text;
        _phone = _phoneController.text;
        _address = _addressController.text;
        _isEditing = false;
      });
      _saveProfileData();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم حفظ التعديلات بنجاح!'),
          backgroundColor: const Color(0xFF00BF63),
        ),
      );
    }
  }

  void _showChangePasswordDialog() {
    showDialog(
      context: context,
      builder: (context) {
        final _oldPass = TextEditingController();
        final _newPass = TextEditingController();
        final _confirmPass = TextEditingController();
        return AlertDialog(
          title: const Text('تغيير كلمة المرور'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _oldPass,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'كلمة المرور الحالية',
                ),
              ),
              TextField(
                controller: _newPass,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'كلمة المرور الجديدة',
                ),
              ),
              TextField(
                controller: _confirmPass,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'تأكيد كلمة المرور',
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                // تحقق من صحة كلمة المرور الجديدة
                if (_newPass.text == _confirmPass.text &&
                    _newPass.text.length >= 6) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تغيير كلمة المرور بنجاح!'),
                      backgroundColor: Color(0xFF00BF63),
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تأكد من تطابق كلمة المرور الجديدة'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = widget.isDark ? const Color(0xFF1B1B1B) : Colors.white;
    final textColor = widget.isDark ? Colors.white : const Color(0xFF1B1B1B);
    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        backgroundColor: bgColor,
        elevation: 0,
        iconTheme: IconThemeData(color: Color(0xFF00BF63)),
        title: Text(
          'الملف الشخصي',
          style: TextStyle(
            color: textColor,
            fontWeight: FontWeight.bold,
            fontFamily: 'Poppins',
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                builder: (_) => HomeScreen(
                  isDark: widget.isDark,
                  onToggleDarkMode: widget.onToggleDarkMode,
                ),
              ),
              (route) => false,
            );
          },
        ),
      ),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
              const SizedBox(height: 30),
              Stack(
                alignment: Alignment.bottomRight,
                children: [
                  CircleAvatar(
                    radius: 48,
                    backgroundColor: Colors.green.withValues(alpha: 0.1),
                    backgroundImage: _profileImage != null
                        ? FileImage(_profileImage!)
                        : null,
                    child: _profileImage == null
                        ? const Icon(
                            Icons.person,
                            size: 60,
                            color: Color(0xFF00BF63),
                          )
                        : null,
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: InkWell(
                      onTap: _pickImage,
                      borderRadius: BorderRadius.circular(20),
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: const Color(0xFF00BF63),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.camera_alt,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              _isEditing
                  ? TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(labelText: 'الاسم'),
                      validator: (v) => v!.isEmpty ? 'مطلوب' : null,
                    )
                  : Text(
                      _name,
                      style: TextStyle(
                        color: textColor,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Poppins',
                      ),
                    ),
              const SizedBox(height: 8),
              _isEditing
                  ? TextFormField(
                      controller: _emailController,
                      decoration: const InputDecoration(
                        labelText: 'البريد الإلكتروني',
                      ),
                      validator: (v) => v!.isEmpty ? 'مطلوب' : null,
                    )
                  : Text(
                      _email,
                      style: TextStyle(
                        color: textColor.withValues(alpha: 0.7),
                        fontSize: 16,
                        fontFamily: 'Poppins',
                      ),
                    ),
              const SizedBox(height: 8),
              _isEditing
                  ? TextFormField(
                      controller: _phoneController,
                      decoration: const InputDecoration(
                        labelText: 'رقم الهاتف',
                      ),
                      validator: (v) => v!.isEmpty ? 'مطلوب' : null,
                    )
                  : Text(
                      _phone,
                      style: TextStyle(
                        color: textColor.withValues(alpha: 0.7),
                        fontSize: 16,
                        fontFamily: 'Poppins',
                      ),
                    ),
              const SizedBox(height: 8),
              _isEditing
                  ? TextFormField(
                      controller: _addressController,
                      decoration: const InputDecoration(labelText: 'العنوان'),
                      validator: (v) => v!.isEmpty ? 'مطلوب' : null,
                    )
                  : Text(
                      _address,
                      style: TextStyle(
                        color: textColor.withValues(alpha: 0.7),
                        fontSize: 16,
                        fontFamily: 'Poppins',
                      ),
                    ),

              const SizedBox(height: 8),
              _isEditing
                  ? TextFormField(
                      controller: _governorateController,
                      decoration: const InputDecoration(labelText: 'المحافظة'),
                      validator: (v) => v!.isEmpty ? 'مطلوب' : null,
                    )
                  : Text(
                      _governorate.isNotEmpty ? _governorate : 'غير محدد',
                      style: TextStyle(
                        color: textColor.withValues(alpha: 0.7),
                        fontSize: 16,
                        fontFamily: 'Poppins',
                      ),
                    ),

              const SizedBox(height: 20),

              // معلومات إضافية
              if (!_isEditing) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: widget.isDark ? Colors.grey[800] : Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'معلومات الحساب',
                        style: TextStyle(
                          color: textColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Poppins',
                        ),
                      ),
                      const SizedBox(height: 12),

                      // حالة التحقق من الإيميل
                      Row(
                        children: [
                          Icon(
                            _isEmailVerified ? Icons.verified : Icons.warning,
                            color: _isEmailVerified ? Colors.green : Colors.orange,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _isEmailVerified ? 'البريد الإلكتروني مؤكد' : 'البريد الإلكتروني غير مؤكد',
                            style: TextStyle(
                              color: textColor.withValues(alpha: 0.8),
                              fontSize: 14,
                              fontFamily: 'Poppins',
                            ),
                          ),
                        ],
                      ),

                      if (_createdAt.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.calendar_today,
                              color: Colors.grey[600],
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'تاريخ إنشاء الحساب: $_createdAt',
                              style: TextStyle(
                                color: textColor.withValues(alpha: 0.7),
                                fontSize: 14,
                                fontFamily: 'Poppins',
                              ),
                            ),
                          ],
                        ),
                      ],

                      if (_lastLogin.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.login,
                              color: Colors.grey[600],
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'آخر تسجيل دخول: $_lastLogin',
                              style: TextStyle(
                                color: textColor.withValues(alpha: 0.7),
                                fontSize: 14,
                                fontFamily: 'Poppins',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                const SizedBox(height: 20),
              ],

              const SizedBox(height: 10),
              ListTile(
                leading: Icon(Icons.lock, color: const Color(0xFF00BF63)),
                title: Text(
                  'تغيير كلمة المرور',
                  style: TextStyle(color: textColor, fontFamily: 'Poppins'),
                ),
                onTap: _showChangePasswordDialog,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: _isEditing
                        ? ElevatedButton.icon(
                            icon: const Icon(Icons.save),
                            label: const Text('حفظ التعديلات'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF00BF63),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              textStyle: const TextStyle(
                                fontFamily: 'Poppins',
                                fontSize: 18,
                              ),
                            ),
                            onPressed: _saveEdit,
                          )
                        : ElevatedButton.icon(
                            icon: const Icon(Icons.edit),
                            label: const Text('تعديل البيانات'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey[700],
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              textStyle: const TextStyle(
                                fontFamily: 'Poppins',
                                fontSize: 18,
                              ),
                            ),
                            onPressed: _startEdit,
                          ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // زر حذف الحساب
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.delete_forever),
                  label: const Text('حذف الحساب'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red[900],
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  onPressed: _isLoading ? null : _deleteAccount,
                ),
              ),

              const SizedBox(height: 12),

              // زر تسجيل الخروج
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.logout),
                  label: const Text('تسجيل الخروج'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red[700],
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    textStyle: const TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 18,
                    ),
                  ),
                  onPressed: _isLoading ? null : _logout,
                ),
              ),
              const SizedBox(height: 20),
              // مساحة إضافية لتجنب overflow
              SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
            ],
          ),
        ),
      ),
    ),
    );
  }
}
