import 'package:flutter/material.dart';
import '../services/user_service.dart';
import 'welcome_screen.dart';
import 'home_screen.dart';

const Color kInDriveGreen = Color(0xFF00BF63);
const Color kDarkGrey = Color(0xFF1B1B1B);

class SplashScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;

  const SplashScreen({
    super.key,
    required this.isDark,
    required this.onToggleDarkMode,
  });

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _checkAuthStatus();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  Future<void> _checkAuthStatus() async {
    // انتظار لمدة ثانيتين لإظهار الشاشة
    await Future.delayed(const Duration(seconds: 2));

    try {
      final isLoggedIn = await UserService.isLoggedIn();
      
      if (mounted) {
        if (isLoggedIn) {
          // المستخدم مسجل دخول، انتقل للصفحة الرئيسية
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => HomeScreen(
                isDark: widget.isDark,
                onToggleDarkMode: widget.onToggleDarkMode,
                onLogout: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => WelcomeScreen(
                        isDark: widget.isDark,
                        onToggleDarkMode: widget.onToggleDarkMode,
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        } else {
          // المستخدم غير مسجل دخول، انتقل لصفحة الترحيب
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => WelcomeScreen(
                isDark: widget.isDark,
                onToggleDarkMode: widget.onToggleDarkMode,
              ),
            ),
          );
        }
      }
    } catch (e) {
      print('Error checking auth status: $e');
      // في حالة الخطأ، انتقل لصفحة الترحيب
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => WelcomeScreen(
              isDark: widget.isDark,
              onToggleDarkMode: widget.onToggleDarkMode,
            ),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = widget.isDark ? kDarkGrey : Colors.white;
    final textColor = widget.isDark ? Colors.white : kDarkGrey;

    return Scaffold(
      backgroundColor: bgColor,
      body: Center(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // شعار التطبيق
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: kInDriveGreen,
                        borderRadius: BorderRadius.circular(30),
                        boxShadow: [
                          BoxShadow(
                            color: kInDriveGreen.withValues(alpha: 0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.local_pharmacy,
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                    
                    const SizedBox(height: 30),
                    
                    // اسم التطبيق
                    Text(
                      'pharmacy store',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: textColor,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                    
                    const SizedBox(height: 10),
                    
                    // شعار فرعي
                    Text(
                      'دوائك في بيتك',
                      style: TextStyle(
                        fontSize: 16,
                        color: textColor.withValues(alpha: 0.7),
                        fontFamily: 'Tajawal',
                      ),
                    ),
                    
                    const SizedBox(height: 50),
                    
                    // مؤشر التحميل
                    SizedBox(
                      width: 30,
                      height: 30,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(const Color.fromARGB(255, 17, 255, 0)),
                        strokeWidth: 3,
                      ),
                    ),
                    
                    const SizedBox(height: 20),
                    
                    Text(
                      'جاري التحميل...',
                      style: TextStyle(
                        fontSize: 14,
                        color: textColor.withValues(alpha: 0.6),
                        fontFamily: 'Tajawal',
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
