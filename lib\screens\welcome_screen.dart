import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/animation.dart';
import 'package:pharmacy/screens/auth_screen.dart';
import 'package:pharmacy/screens/pharmacy_login_screen.dart';
import 'package:pharmacy/screens/user_login_screen.dart';
import 'package:pharmacy/screens/home_screen.dart';
import 'package:pharmacy/test_api.dart';
// import 'package:pharmacy/utils/localization/my_app_locale.dart';
import 'package:easy_localization/easy_localization.dart';
import 'dart:math' as math;

class WelcomeScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;
  final void Function(Locale)? onLocaleChanged;

  const WelcomeScreen({
    Key? key,
    required this.isDark,
    required this.onToggleDarkMode,
    this.onLocaleChanged,
  }) : super(key: key);

  @override
  _WelcomeScreenState createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _themeController;
  late AnimationController _typewriterController;
  late AnimationController _cursorController;
  late AnimationController _logoBreathController;
  late AnimationController _logoRotationController;
  late AnimationController _logoColorController;
  late AnimationController _wavesController;
  late AnimationController _buttonPulseController;
  late AnimationController _buttonHoverController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _themeTransition;
  late Animation<double> _iconRotation;
  late Animation<int> _typewriterAnimation;
  late Animation<double> _cursorBlink;
  late Animation<double> _logoBreath;
  late Animation<double> _logoRotation;
  late Animation<double> _logoColorShift;
  late Animation<double> _wavesAnimation;
  late Animation<double> _buttonPulse;
  late Animation<double> _buttonHover;
  String lang = 'العربية';
  late bool _currentDarkMode;

  @override
  void initState() {
    super.initState();
    _currentDarkMode = widget.isDark;

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _themeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _typewriterController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3000), // 3 ثواني للكتابة الكاملة
    );

    _cursorController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // تهيئة controllers الشعار
    _logoBreathController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    _logoRotationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 8000),
    );

    _logoColorController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 4000),
    );

    // تهيئة controller تأثيرات الخلفية
    _wavesController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 6000),
    );

    // تهيئة controllers الأزرار
    _buttonPulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    _buttonHoverController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeInOut),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.3, 0.8, curve: Curves.elasticOut),
      ),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _controller,
            curve: const Interval(0.5, 1.0, curve: Curves.easeOut),
          ),
        );

    _themeTransition = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _themeController,
        curve: Curves.easeInOutCubic,
      ),
    );

    _iconRotation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _themeController,
        curve: Curves.elasticOut,
      ),
    );

    // تهيئة انيميشن الكتابة
    final fullTitle = 'appTitle'.tr();
    _typewriterAnimation = IntTween(begin: 0, end: fullTitle.length).animate(
      CurvedAnimation(
        parent: _typewriterController,
        curve: Curves.easeInOut,
      ),
    );

    // تهيئة انيميشن وميض المؤشر
    _cursorBlink = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _cursorController,
        curve: Curves.easeInOut,
      ),
    );

    // تهيئة انيميشنز الشعار
    _logoBreath = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(
        parent: _logoBreathController,
        curve: Curves.easeInOut,
      ),
    );

    _logoRotation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoRotationController,
        curve: Curves.linear,
      ),
    );

    _logoColorShift = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoColorController,
        curve: Curves.easeInOut,
      ),
    );

    // تهيئة انيميشن الخلفية
    _wavesAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _wavesController,
        curve: Curves.easeInOut,
      ),
    );

    // تهيئة انيميشنز الأزرار
    _buttonPulse = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(
        parent: _buttonPulseController,
        curve: Curves.easeInOut,
      ),
    );

    _buttonHover = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _buttonHoverController,
        curve: Curves.easeOut,
      ),
    );

    _controller.forward();
    _startTypewriterAnimation();
    _cursorController.repeat(reverse: true); // وميض مستمر

    // بدء انيميشنز الشعار
    _logoBreathController.repeat(reverse: true);
    _logoRotationController.repeat();
    _logoColorController.repeat(reverse: true);

    // بدء انيميشن الخلفية
    _wavesController.repeat(reverse: true);

    // بدء انيميشن نبض الأزرار
    _buttonPulseController.repeat(reverse: true);
  }

  void _startTypewriterAnimation() {
    _typewriterController.forward().then((_) {
      // انتظار ثانية واحدة ثم إعادة البدء
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          _typewriterController.reset();
          _startTypewriterAnimation();
        }
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _themeController.dispose();
    _typewriterController.dispose();
    _cursorController.dispose();
    _logoBreathController.dispose();
    _logoRotationController.dispose();
    _logoColorController.dispose();
    _wavesController.dispose();
    _buttonPulseController.dispose();
    _buttonHoverController.dispose();
    super.dispose();
  }

  void _changeLanguage(String? value) {
    if (value == null) return;
    setState(() {
      lang = value;
    });
    Locale newLocale = value == 'English'
        ? const Locale('en')
        : const Locale('ar');
    EasyLocalization.of(context)!.setLocale(newLocale);
  }

  void _handleDarkModeToggle() {
    // تشغيل انيميشن التحويل
    _themeController.forward().then((_) {
      setState(() {
        _currentDarkMode = !_currentDarkMode;
      });
      widget.onToggleDarkMode();
      _themeController.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = _currentDarkMode ? const Color(0xFF121212) : Colors.white;
    final textColor = _currentDarkMode ? Colors.white : const Color(0xFF121212);
    const primaryColor = Color(0xFF00BF63);
    return Scaffold(
      backgroundColor: bgColor,
      body: SafeArea(
        child: Stack(
          children: [
            // زر اختبار API
            Positioned(
              top: 16,
              right: 16,
              child: FloatingActionButton.small(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const TestApiScreen(),
                    ),
                  );
                },
                backgroundColor: primaryColor,
                child: const Icon(Icons.bug_report, color: Colors.white),
              ),
            ),
            // خلفية متحركة مع تأثيرات متقدمة
            Positioned.fill(
              child: AnimatedBuilder(
                animation: _themeTransition,
                builder: (context, child) {
                  return AnimatedContainer(
                    key: ValueKey(_currentDarkMode),
                    duration: const Duration(milliseconds: 800),
                    curve: Curves.easeInOutCubic,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: _currentDarkMode
                            ? [
                                const Color(0xFF121212),
                                const Color(0xFF1E1E1E),
                                const Color(0xFF2A2A2A),
                              ]
                            : [
                                Colors.white,
                                const Color(0xFFF5F5F5),
                                const Color(0xFFE8E8E8),
                              ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // طبقة الموجات المتحركة الناعمة
            Positioned.fill(
              child: AnimatedBuilder(
                animation: _wavesAnimation,
                builder: (context, child) {
                  return CustomPaint(
                    painter: WavesPainter(
                      animation: _wavesAnimation.value,
                      isDark: _currentDarkMode,
                    ),
                  );
                },
              ),
            ),

            // طبقة التأثير الدائري عند تغيير الثيم
            Positioned.fill(
              child: AnimatedBuilder(
                animation: _themeTransition,
                builder: (context, child) {
                  return AnimatedOpacity(
                    opacity: _themeTransition.value * 0.6,
                    duration: const Duration(milliseconds: 400),
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: RadialGradient(
                          center: Alignment.center,
                          radius: 1.0 + (_themeTransition.value * 0.8),
                          colors: [
                            Colors.transparent,
                            const Color(0xFF00BF63).withValues(alpha: _themeTransition.value * 0.1),
                            (_currentDarkMode ? Colors.white : Colors.black)
                                .withValues(alpha: _themeTransition.value * 0.05),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            // عناصر التحكم العلوية
            Positioned(
              top: 16,
              right: 16,
              child: Row(
                children: [
                  // زر اللغة
                  Container(
                    decoration: BoxDecoration(
                      color: _currentDarkMode ? Colors.white10 : Colors.grey[200],
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: lang,
                        icon: const Icon(Icons.language, size: 20),
                        style: TextStyle(
                          color: textColor,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.bold,
                        ),
                        dropdownColor: bgColor,
                        borderRadius: BorderRadius.circular(20),
                        items: [
                          DropdownMenuItem(
                            value: 'العربية',
                            child: Text('arabic'.tr()),
                          ),
                          DropdownMenuItem(
                            value: 'English',
                            child: Text('english'.tr()),
                          ),
                        ],
                        onChanged: _changeLanguage,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // زر الوضع الليلي مع انيميشن
                  AnimatedBuilder(
                    animation: _iconRotation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _iconRotation.value * 6.28, // دورة كاملة
                        child: AnimatedScale(
                          scale: 1.0 + (_themeTransition.value * 0.2),
                          duration: const Duration(milliseconds: 300),
                          child: IconButton(
                            icon: AnimatedSwitcher(
                              duration: const Duration(milliseconds: 300),
                              transitionBuilder: (child, animation) {
                                return RotationTransition(
                                  turns: animation,
                                  child: ScaleTransition(
                                    scale: animation,
                                    child: child,
                                  ),
                                );
                              },
                              child: Icon(
                                _currentDarkMode ? Icons.light_mode : Icons.dark_mode,
                                key: ValueKey(_currentDarkMode),
                                color: primaryColor,
                                size: 28,
                              ),
                            ),
                            onPressed: _handleDarkModeToggle,
                            tooltip: 'darkMode'.tr(),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            // المحتوى الرئيسي
            Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // شعار متحرك مع تأثيرات متقدمة
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: ScaleTransition(
                        scale: _scaleAnimation,
                        child: AnimatedBuilder(
                          animation: Listenable.merge([
                            _themeTransition,
                            _logoBreath,
                            _logoRotation,
                            _logoColorShift,
                          ]),
                          builder: (context, child) {
                            // حساب الألوان المتغيرة
                            final colorProgress = _logoColorShift.value;
                            final baseColor = Color.lerp(
                              primaryColor,
                              Color.fromARGB(
                                255,
                                ((primaryColor.r * 255.0).round() * 0.8).clamp(0, 255).toInt(),
                                (primaryColor.g * 255.0).round(),
                                ((primaryColor.b * 255.0).round() * 1.2).clamp(0, 255).toInt(),
                              ),
                              colorProgress,
                            )!;

                            return Transform.scale(
                              scale: _logoBreath.value,
                              child: Transform.rotate(
                                angle: _logoRotation.value * 0.1, // دوران بطيء وناعم
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 600),
                                  decoration: BoxDecoration(
                                    gradient: RadialGradient(
                                      colors: [
                                        baseColor.withValues(alpha: 0.15 + (_themeTransition.value * 0.1)),
                                        baseColor.withValues(alpha: 0.08 + (_themeTransition.value * 0.05)),
                                        baseColor.withValues(alpha: 0.02),
                                      ],
                                      stops: const [0.0, 0.7, 1.0],
                                    ),
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      // ظل داخلي متحرك
                                      BoxShadow(
                                        color: baseColor.withValues(alpha: 0.3 + (_logoBreath.value - 0.95) * 2),
                                        blurRadius: 15 + (_logoBreath.value - 0.95) * 50,
                                        spreadRadius: 1,
                                      ),
                                      // ظل خارجي ملون
                                      BoxShadow(
                                        color: baseColor.withValues(alpha: 0.2),
                                        blurRadius: 25 + (_themeTransition.value * 15),
                                        spreadRadius: 3,
                                        offset: Offset(
                                          math.sin(_logoRotation.value * 6.28) * 2,
                                          math.cos(_logoRotation.value * 6.28) * 2,
                                        ),
                                      ),
                                    ],
                                  ),
                                  padding: const EdgeInsets.all(24),
                                  child: Transform.scale(
                                    scale: 1.0 + (_logoColorShift.value * 0.05),
                                    child: SvgPicture.asset(
                                      'assets/images/pharmacy_welcome.svg',
                                      width: 150,
                                      height: 150,
                                      colorFilter: ColorFilter.mode(
                                        baseColor.withValues(alpha: 0.8 + (_logoColorShift.value * 0.2)),
                                        BlendMode.modulate,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),

                    const SizedBox(height: 32),

                    // عنوان التطبيق مع تأثير الكتابة
                    SlideTransition(
                      position: _slideAnimation,
                      child: AnimatedBuilder(
                        animation: Listenable.merge([_themeTransition, _typewriterAnimation]),
                        builder: (context, child) {
                          final fullTitle = 'appTitle'.tr();
                          final displayedText = fullTitle.substring(0, _typewriterAnimation.value);

                          return Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                displayedText,
                                style: TextStyle(
                                  fontSize: 36,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: 'Tajawal',
                                  letterSpacing: 1.2,
                                  color: primaryColor,
                                  shadows: [
                                    Shadow(
                                      offset: const Offset(2, 2),
                                      blurRadius: 4,
                                      color: _currentDarkMode
                                        ? Colors.black.withValues(alpha: 0.5)
                                        : Colors.grey.withValues(alpha: 0.3),
                                    ),
                                  ],
                                ),
                              ),
                              // مؤشر الكتابة المتحرك مع وميض
                              AnimatedBuilder(
                                animation: _cursorBlink,
                                builder: (context, child) {
                                  return AnimatedOpacity(
                                    opacity: (_typewriterAnimation.value < fullTitle.length)
                                      ? _cursorBlink.value
                                      : 0.0,
                                    duration: const Duration(milliseconds: 100),
                                    child: Container(
                                      width: 3,
                                      height: 36,
                                      margin: const EdgeInsets.only(left: 2),
                                      decoration: BoxDecoration(
                                        color: primaryColor,
                                        borderRadius: BorderRadius.circular(2),
                                        boxShadow: [
                                          BoxShadow(
                                            color: primaryColor.withValues(alpha: 0.5),
                                            blurRadius: 4,
                                            spreadRadius: 1,
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          );
                        },
                      ),
                    ),

                    const SizedBox(height: 16),

                    // وصف التطبيق
                    SlideTransition(
                      position: _slideAnimation,
                      child: Text(
                        'welcome'.tr(),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 18,
                          color: textColor.withValues(alpha: 0.8),
                          fontFamily: 'Tajawal',
                          height: 1.6,
                        ),
                      ),
                    ),

                    const SizedBox(height: 48),

                    // أزرار الدخول مع تأثيرات متقدمة
                    SlideTransition(
                      position: _slideAnimation,
                      child: Column(
                        children: [
                          // زر الدخول كمستخدم مع تأثيرات
                          AnimatedBuilder(
                            animation: Listenable.merge([_buttonPulse, _buttonHover]),
                            builder: (context, child) {
                              return Transform.scale(
                                scale: _buttonPulse.value,
                                child: Container(
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(16),
                                    gradient: LinearGradient(
                                      colors: [
                                        primaryColor,
                                        primaryColor.withValues(
                                          red: (primaryColor.r * 255 * 0.9).round() / 255,
                                          green: primaryColor.g,
                                          blue: (primaryColor.b * 255 * 1.1).clamp(0, 255).round() / 255,
                                        ),
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: primaryColor.withValues(alpha: 0.4 + (_buttonPulse.value - 1.0) * 2),
                                        blurRadius: 15 + (_buttonPulse.value - 1.0) * 20,
                                        spreadRadius: 2,
                                        offset: const Offset(0, 4),
                                      ),
                                      BoxShadow(
                                        color: primaryColor.withValues(alpha: 0.2),
                                        blurRadius: 30,
                                        spreadRadius: -5,
                                        offset: const Offset(0, 10),
                                      ),
                                    ],
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(16),
                                      onTap: _navigateToUserAuth,
                                      onTapDown: (_) => _buttonHoverController.forward(),
                                      onTapUp: (_) => _buttonHoverController.reverse(),
                                      onTapCancel: () => _buttonHoverController.reverse(),
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(vertical: 20),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.person,
                                              color: Colors.white,
                                              size: 24,
                                            ),
                                            const SizedBox(width: 12),
                                            Text(
                                              'loginAsUser'.tr(),
                                              style: const TextStyle(
                                                fontSize: 18,
                                                fontFamily: 'Tajawal',
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),

                          const SizedBox(height: 20),

                          // زر الدخول كصيدلية مع تأثيرات
                          AnimatedBuilder(
                            animation: Listenable.merge([_buttonPulse, _buttonHover]),
                            builder: (context, child) {
                              return Transform.scale(
                                scale: _buttonPulse.value * 0.98, // تأثير أقل للزر الثانوي
                                child: Container(
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: primaryColor,
                                      width: 2.5,
                                    ),
                                    gradient: LinearGradient(
                                      colors: [
                                        (_currentDarkMode ? Colors.white : Colors.black).withValues(alpha: 0.05),
                                        primaryColor.withValues(alpha: 0.1),
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: primaryColor.withValues(alpha: 0.2 + (_buttonPulse.value - 1.0) * 1.5),
                                        blurRadius: 10 + (_buttonPulse.value - 1.0) * 15,
                                        spreadRadius: 1,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(16),
                                      onTap: _navigateToPharmacyLogin,
                                      onTapDown: (_) => _buttonHoverController.forward(),
                                      onTapUp: (_) => _buttonHoverController.reverse(),
                                      onTapCancel: () => _buttonHoverController.reverse(),
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(vertical: 20),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.local_pharmacy,
                                              color: primaryColor,
                                              size: 24,
                                            ),
                                            const SizedBox(width: 12),
                                            Text(
                                              'loginAsPharmacy'.tr(),
                                              style: TextStyle(
                                                fontSize: 18,
                                                fontFamily: 'Tajawal',
                                                fontWeight: FontWeight.bold,
                                                color: primaryColor,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToUserAuth() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => const UserLoginScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1, 0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToPharmacyLogin() {
    Navigator.pushReplacement(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            PharmacyLoginScreen(
              isDark: widget.isDark,
              onToggleDarkMode: widget.onToggleDarkMode,
            ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1, 0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }
}

// رسام الموجات المتحركة الناعمة
class WavesPainter extends CustomPainter {
  final double animation;
  final bool isDark;

  WavesPainter({required this.animation, required this.isDark});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    const primaryColor = Color(0xFF00BF63);
    final waveCount = 2; // تقليل عدد الموجات

    for (int wave = 0; wave < waveCount; wave++) {
      final path = Path();
      final waveOffset = (animation + wave * 0.5) * 2 * math.pi;
      final opacity = (0.05 + 0.1 * math.sin(animation * 2 * math.pi + wave)) * (isDark ? 0.4 : 0.2);

      paint.color = primaryColor.withValues(alpha: opacity);

      path.moveTo(0, size.height * (0.3 + wave * 0.4));

      for (double x = 0; x <= size.width; x += 8) {
        final y = size.height * (0.3 + wave * 0.4) +
                 20 * math.sin((x / size.width) * 3 * math.pi + waveOffset) +
                 10 * math.sin((x / size.width) * 6 * math.pi + waveOffset * 1.2);
        path.lineTo(x, y);
      }

      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}


