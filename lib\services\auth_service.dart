import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class AuthService {
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _userDataKey = 'user_data';
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';

  // حفظ بيانات تسجيل الدخول
  static Future<void> saveLoginData({
    required Map<String, dynamic> userData,
    required String accessToken,
    required String refreshToken,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setBool(_isLoggedInKey, true);
    await prefs.setString(_userDataKey, jsonEncode(userData));
    await prefs.setString(_accessTokenKey, accessToken);
    await prefs.setString(_refreshTokenKey, refreshToken);
    
    print('✅ تم حفظ بيانات تسجيل الدخول');
  }

  // التحقق من حالة تسجيل الدخول
  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isLoggedInKey) ?? false;
  }

  // الحصول على بيانات المستخدم المحفوظة
  static Future<Map<String, dynamic>?> getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString(_userDataKey);
    
    if (userDataString != null) {
      return jsonDecode(userDataString);
    }
    return null;
  }

  // الحصول على Access Token
  static Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_accessTokenKey);
  }

  // الحصول على Refresh Token
  static Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_refreshTokenKey);
  }

  // تحديث Access Token
  static Future<void> updateAccessToken(String newAccessToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_accessTokenKey, newAccessToken);
    print('✅ تم تحديث Access Token');
  }

  // تسجيل الخروج وحذف جميع البيانات
  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.remove(_isLoggedInKey);
    await prefs.remove(_userDataKey);
    await prefs.remove(_accessTokenKey);
    await prefs.remove(_refreshTokenKey);
    
    print('✅ تم تسجيل الخروج وحذف جميع البيانات');
  }

  // الحصول على جميع بيانات تسجيل الدخول
  static Future<Map<String, dynamic>?> getLoginData() async {
    final isLoggedIn = await AuthService.isLoggedIn();
    if (!isLoggedIn) return null;

    final userData = await getUserData();
    final accessToken = await getAccessToken();
    final refreshToken = await getRefreshToken();

    if (userData != null && accessToken != null && refreshToken != null) {
      return {
        'user': userData,
        'accessToken': accessToken,
        'refreshToken': refreshToken,
      };
    }

    return null;
  }

  // طباعة حالة تسجيل الدخول للتشخيص
  static Future<void> printLoginStatus() async {
    final isLoggedIn = await AuthService.isLoggedIn();
    final userData = await getUserData();
    final accessToken = await getAccessToken();
    
    print('=== حالة تسجيل الدخول ===');
    print('مسجل دخول: $isLoggedIn');
    print('بيانات المستخدم: ${userData?['name'] ?? 'غير متوفر'}');
    print('Access Token: ${accessToken != null ? 'متوفر' : 'غير متوفر'}');
    print('========================');
  }
}
