import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';
import 'pharmacy_service.dart';
import 'user_service.dart';

class OrderService {
  // Headers للطلبات
  static Map<String, String> get headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Headers مع التوكن للمستخدم
  static Future<Map<String, String>> get userAuthHeaders async {
    final token = await UserService.getAccessToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Headers مع التوكن للصيدلية
  static Future<Map<String, String>> get pharmacyAuthHeaders async {
    final token = await PharmacyService.getAccessToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// إنشاء طلب جديد من المستخدم
  static Future<Map<String, dynamic>> createOrder({
    required String medicineName,
    required int quantity,
    String? description,
    String? imageUrl,
    String? prescriptionImageUrl,
    String? userGovernorate,
  }) async {
    try {
      print('📝 إنشاء طلب جديد...');
      AppConfig.printNetworkInfo();

      final requestBody = {
        'medicineName': medicineName,
        'quantity': quantity,
        'description': description,
        'imageUrl': imageUrl,
        'prescriptionImageUrl': prescriptionImageUrl,
        'userGovernorate': userGovernorate ?? 'القاهرة', // المحافظة الافتراضية
        'currency': 'EGP', // العملة بالجنيه المصري
      };

      print('📤 إرسال بيانات الطلب: ${jsonEncode(requestBody)}');

      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}/orders'),
        headers: await userAuthHeaders,
        body: jsonEncode(requestBody),
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');
      print('📄 محتوى الاستجابة: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 201 && responseData['success'] == true) {
        return {
          'success': true,
          'message': responseData['message'] ?? 'تم إنشاء الطلب بنجاح',
          'data': responseData['data'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في إنشاء الطلب',
          'errors': responseData['errors'],
        };
      }
    } catch (e) {
      print('❌ خطأ في إنشاء الطلب: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
      };
    }
  }

  /// الحصول على طلبات المستخدم
  static Future<Map<String, dynamic>> getUserOrders({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    try {
      print('📋 جلب طلبات المستخدم...');

      String url = '${AppConfig.baseUrl}/orders/user?page=$page&limit=$limit';
      if (status != null) {
        url += '&status=$status';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: await userAuthHeaders,
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        return {
          'success': true,
          'data': responseData['data'],
          'pagination': responseData['pagination'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في جلب الطلبات',
        };
      }
    } catch (e) {
      print('❌ خطأ في جلب طلبات المستخدم: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
      };
    }
  }

  /// الحصول على طلب محدد بـ ID (للعميل)
  static Future<Map<String, dynamic>> getOrderById({
    required String orderId,
  }) async {
    try {
      print('📋 جلب الطلب $orderId...');

      // التحقق من وجود التوكن
      final token = await UserService.getAccessToken();
      if (token == null) {
        print('❌ لا يوجد توكن مصادقة');
        return {
          'success': false,
          'message': 'يجب تسجيل الدخول أولاً',
          'needLogin': true,
        };
      }

      final response = await http.get(
        Uri.parse('${AppConfig.baseUrl}/orders/$orderId'),
        headers: await userAuthHeaders,
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');

      if (response.statusCode == 401) {
        print('❌ التوكن منتهي الصلاحية أو غير صالح');
        return {
          'success': false,
          'message': 'انتهت صلاحية جلسة المستخدم، يرجى تسجيل الدخول مرة أخرى',
          'needLogin': true,
        };
      }

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        return {
          'success': true,
          'data': responseData['data'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في جلب الطلب',
        };
      }
    } catch (e) {
      print('❌ خطأ في جلب الطلب: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
      };
    }
  }

  /// الحصول على طلبات الصيدلية
  static Future<Map<String, dynamic>> getPharmacyOrders({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    try {
      print('🏥 جلب طلبات الصيدلية...');

      String url = '${AppConfig.baseUrl}/orders/pharmacy?page=$page&limit=$limit';
      if (status != null) {
        url += '&status=$status';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: await pharmacyAuthHeaders,
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        return {
          'success': true,
          'data': responseData['data'],
          'pagination': responseData['pagination'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في جلب الطلبات',
        };
      }
    } catch (e) {
      print('❌ خطأ في جلب طلبات الصيدلية: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
      };
    }
  }

  /// الرد على طلب (من الصيدلية)
  static Future<Map<String, dynamic>> respondToOrder({
    required String orderId,
    required String status, // 'accepted', 'rejected'
    double? price,
    String? notes,
    int? availableQuantity,
    String? alternativeMedicine,
  }) async {
    try {
      print('💬 الرد على الطلب $orderId...');

      final requestBody = {
        'status': status,
        'price': price,
        'notes': notes,
        'availableQuantity': availableQuantity,
        'alternativeMedicine': alternativeMedicine,
        'currency': 'EGP', // العملة بالجنيه المصري
      };

      print('📤 إرسال رد الصيدلية: ${jsonEncode(requestBody)}');

      final response = await http.put(
        Uri.parse('${AppConfig.baseUrl}/orders/$orderId/respond'),
        headers: await pharmacyAuthHeaders,
        body: jsonEncode(requestBody),
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        return {
          'success': true,
          'message': responseData['message'] ?? 'تم الرد على الطلب بنجاح',
          'data': responseData['data'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في الرد على الطلب',
        };
      }
    } catch (e) {
      print('❌ خطأ في الرد على الطلب: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
      };
    }
  }

  /// تأكيد الطلب من المستخدم
  static Future<Map<String, dynamic>> confirmOrder({
    required String orderId,
    required String action, // 'confirm', 'cancel'
  }) async {
    try {
      print('✅ تأكيد الطلب $orderId...');

      final requestBody = {
        'action': action,
      };

      final response = await http.put(
        Uri.parse('${AppConfig.baseUrl}/orders/$orderId/confirm'),
        headers: await userAuthHeaders,
        body: jsonEncode(requestBody),
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        return {
          'success': true,
          'message': responseData['message'] ?? 'تم تأكيد الطلب بنجاح',
          'data': responseData['data'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في تأكيد الطلب',
        };
      }
    } catch (e) {
      print('❌ خطأ في تأكيد الطلب: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
      };
    }
  }

  /// تحديث حالة الطلب
  static Future<Map<String, dynamic>> updateOrderStatus({
    required String orderId,
    required String status, // 'preparing', 'ready', 'delivered'
    String? notes,
  }) async {
    try {
      print('🔄 تحديث حالة الطلب $orderId إلى $status...');

      final requestBody = {
        'status': status,
        'notes': notes,
      };

      final response = await http.put(
        Uri.parse('${AppConfig.baseUrl}/orders/$orderId/status'),
        headers: await pharmacyAuthHeaders,
        body: jsonEncode(requestBody),
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');
      print('📄 محتوى الاستجابة: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        return {
          'success': true,
          'message': responseData['message'] ?? 'تم تحديث حالة الطلب بنجاح',
          'data': responseData['data'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في تحديث حالة الطلب',
        };
      }
    } catch (e) {
      print('❌ خطأ في تحديث حالة الطلب: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
      };
    }
  }

  /// تقديم عرض على طلب دواء (من الصيدلية)
  static Future<Map<String, dynamic>> submitOffer({
    required String orderId,
    required double price,
    required int availableQuantity,
    String? notes,
    int? estimatedTime,
  }) async {
    try {
      print('💰 تقديم عرض على الطلب $orderId...');

      final requestBody = {
        'price': price,
        'availableQuantity': availableQuantity,
        'notes': notes,
        'estimatedTime': estimatedTime ?? 30,
      };

      print('📤 إرسال العرض: ${jsonEncode(requestBody)}');

      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}/orders/$orderId/offer'),
        headers: await pharmacyAuthHeaders,
        body: jsonEncode(requestBody),
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 201 && responseData['success'] == true) {
        return {
          'success': true,
          'message': responseData['message'] ?? 'تم تقديم العرض بنجاح',
          'data': responseData['data'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في تقديم العرض',
        };
      }
    } catch (e) {
      print('❌ خطأ في تقديم العرض: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
      };
    }
  }

  /// الحصول على العروض لطلب معين (للمستخدم)
  static Future<Map<String, dynamic>> getOrderOffers({
    required String orderId,
  }) async {
    try {
      print('📋 جلب العروض للطلب $orderId...');

      final response = await http.get(
        Uri.parse('${AppConfig.baseUrl}/orders/$orderId/offers'),
        headers: await userAuthHeaders,
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        return {
          'success': true,
          'data': responseData['data'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في جلب العروض',
        };
      }
    } catch (e) {
      print('❌ خطأ في جلب العروض: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
      };
    }
  }

  /// قبول عرض من صيدلية (للمستخدم)
  static Future<Map<String, dynamic>> acceptOffer({
    required String orderId,
    required String offerId,
  }) async {
    try {
      print('✅ قبول العرض $offerId للطلب $orderId...');

      final response = await http.put(
        Uri.parse('${AppConfig.baseUrl}/orders/$orderId/offers/$offerId/accept'),
        headers: await userAuthHeaders,
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        return {
          'success': true,
          'message': responseData['message'] ?? 'تم قبول العرض بنجاح',
          'data': responseData['data'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في قبول العرض',
        };
      }
    } catch (e) {
      print('❌ خطأ في قبول العرض: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
      };
    }
  }
}
