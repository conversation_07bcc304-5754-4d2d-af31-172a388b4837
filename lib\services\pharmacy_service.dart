import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';

class PharmacyService {
  static const String _pharmacyDataKey = 'pharmacy_data';
  static const String _pharmacyTokenKey = 'pharmacy_access_token';
  static const String _pharmacyRefreshTokenKey = 'pharmacy_refresh_token';
  static const String _isPharmacyLoggedInKey = 'is_pharmacy_logged_in';

  // Headers للطلبات
  static Map<String, String> get headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Headers مع التوكن
  static Future<Map<String, String>> get authHeaders async {
    final token = await getAccessToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// تسجيل صيدلية جديدة
  static Future<Map<String, dynamic>> registerPharmacy({
    required String name,
    required String email,
    required String phone,
    required String password,
    required String licenseNumber,
    required String address,
    String? city,
    required String governorate,
    required String ownerName,
    required String ownerNationalId,
    Map<String, double>? coordinates,
    Map<String, dynamic>? workingHours,
    Map<String, dynamic>? services,
  }) async {
    try {
      print('🏥 محاولة تسجيل صيدلية جديدة...');
      AppConfig.printNetworkInfo();

      final requestBody = {
        'name': name,
        'email': email,
        'phone': phone,
        'password': password,
        'licenseNumber': licenseNumber,
        'address': {
          'street': address,
          'city': city ?? address.split(',').first.trim(),
          'governorate': governorate,
        },
        'location': coordinates != null ? {
          'type': 'Point',
          'coordinates': [coordinates['longitude']!, coordinates['latitude']!]
        } : {
          'type': 'Point',
          'coordinates': [31.2357, 30.0444] // إحداثيات افتراضية للقاهرة
        },
        'ownerName': ownerName,
        'ownerNationalId': ownerNationalId,
        if (workingHours != null) 'workingHours': workingHours,
        if (services != null) 'services': services,
      };

      print('📤 إرسال بيانات التسجيل: ${jsonEncode(requestBody)}');

      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}/pharmacy/register'),
        headers: headers,
        body: jsonEncode(requestBody),
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');
      print('📄 محتوى الاستجابة: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 201 && responseData['success'] == true) {
        // حفظ بيانات الصيدلية والتوكن
        final pharmacyData = responseData['data']['pharmacy'];
        final accessToken = responseData['data']['accessToken'];
        final refreshToken = responseData['data']['refreshToken'];

        await _savePharmacyData(pharmacyData, accessToken, refreshToken);

        return {
          'success': true,
          'message': responseData['message'] ?? 'تم تسجيل الصيدلية بنجاح',
          'data': responseData['data'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في تسجيل الصيدلية',
          'errors': responseData['errors'],
        };
      }
    } catch (e) {
      print('❌ خطأ في تسجيل الصيدلية: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
      };
    }
  }

  /// تسجيل دخول الصيدلية
  static Future<Map<String, dynamic>> loginPharmacy({
    required String email,
    required String password,
  }) async {
    try {
      print('🏥 محاولة تسجيل دخول الصيدلية...');
      AppConfig.printNetworkInfo();

      final requestBody = {
        'email': email,
        'password': password,
      };

      print('📤 إرسال بيانات تسجيل الدخول');

      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}/pharmacy/login'),
        headers: headers,
        body: jsonEncode(requestBody),
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');
      print('📄 محتوى الاستجابة: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        // حفظ بيانات الصيدلية والتوكن
        final pharmacyData = responseData['data']['pharmacy'];
        final accessToken = responseData['data']['accessToken'];
        final refreshToken = responseData['data']['refreshToken'];

        await _savePharmacyData(pharmacyData, accessToken, refreshToken);

        return {
          'success': true,
          'message': responseData['message'] ?? 'تم تسجيل الدخول بنجاح',
          'data': responseData['data'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في تسجيل الدخول',
          'errors': responseData['errors'],
        };
      }
    } catch (e) {
      print('❌ خطأ في تسجيل دخول الصيدلية: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
      };
    }
  }

  /// حفظ بيانات الصيدلية محلياً
  static Future<void> _savePharmacyData(
    Map<String, dynamic> pharmacyData,
    String accessToken,
    String refreshToken,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setString(_pharmacyDataKey, jsonEncode(pharmacyData));
    await prefs.setString(_pharmacyTokenKey, accessToken);
    await prefs.setString(_pharmacyRefreshTokenKey, refreshToken);
    await prefs.setBool(_isPharmacyLoggedInKey, true);
    
    print('✅ تم حفظ بيانات الصيدلية محلياً');
  }

  /// الحصول على بيانات الصيدلية
  static Future<Map<String, dynamic>?> getPharmacyData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pharmacyDataString = prefs.getString(_pharmacyDataKey);
      
      if (pharmacyDataString != null) {
        return jsonDecode(pharmacyDataString);
      }
      return null;
    } catch (e) {
      print('خطأ في الحصول على بيانات الصيدلية: $e');
      return null;
    }
  }

  /// الحصول على التوكن
  static Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_pharmacyTokenKey);
  }

  /// التحقق من حالة تسجيل الدخول
  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isPharmacyLoggedInKey) ?? false;
  }

  /// تسجيل الخروج
  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.remove(_pharmacyDataKey);
    await prefs.remove(_pharmacyTokenKey);
    await prefs.remove(_pharmacyRefreshTokenKey);
    await prefs.setBool(_isPharmacyLoggedInKey, false);
    
    print('✅ تم تسجيل خروج الصيدلية');
  }

  /// الحصول على اسم الصيدلية
  static Future<String> getPharmacyName() async {
    final pharmacyData = await getPharmacyData();
    return pharmacyData?['name'] ?? 'الصيدلية';
  }

  /// الحصول على إيميل الصيدلية
  static Future<String> getPharmacyEmail() async {
    final pharmacyData = await getPharmacyData();
    return pharmacyData?['email'] ?? '';
  }

  /// الحصول على رقم هاتف الصيدلية
  static Future<String> getPharmacyPhone() async {
    final pharmacyData = await getPharmacyData();
    return pharmacyData?['phone'] ?? '';
  }

  /// الحصول على عنوان الصيدلية
  static Future<String> getPharmacyAddress() async {
    final pharmacyData = await getPharmacyData();
    return pharmacyData?['address'] ?? '';
  }

  /// الحصول على رقم الترخيص
  static Future<String> getLicenseNumber() async {
    final pharmacyData = await getPharmacyData();
    return pharmacyData?['licenseNumber'] ?? '';
  }

  /// تحديث بيانات الصيدلية
  static Future<void> updatePharmacyData(Map<String, dynamic> newData) async {
    final currentData = await getPharmacyData();
    if (currentData != null) {
      currentData.addAll(newData);
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_pharmacyDataKey, jsonEncode(currentData));
    }
  }
}
