import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';
import 'pharmacy_service.dart';
import 'user_service.dart';

class WalletService {
  // Headers للطلبات
  static Map<String, String> get headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Headers مع التوكن للمستخدم
  static Future<Map<String, String>> get userAuthHeaders async {
    final token = await UserService.getAccessToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Headers مع التوكن للصيدلية
  static Future<Map<String, String>> get pharmacyAuthHeaders async {
    final token = await PharmacyService.getAccessToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// الحصول على رصيد المحفظة للصيدلية
  static Future<Map<String, dynamic>> getPharmacyWalletBalance() async {
    try {
      print('💰 جلب رصيد محفظة الصيدلية...');
      AppConfig.printNetworkInfo();

      final response = await http.get(
        Uri.parse('${AppConfig.baseUrl}/wallet/pharmacy/balance'),
        headers: await pharmacyAuthHeaders,
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');
      print('📄 محتوى الاستجابة: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        return {
          'success': true,
          'balance': responseData['data']['balance'] ?? 0.0,
          'currency': responseData['data']['currency'] ?? 'EGP',
          'lastUpdated': responseData['data']['lastUpdated'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في جلب رصيد المحفظة',
          'balance': 0.0,
          'currency': 'EGP',
        };
      }
    } catch (e) {
      print('❌ خطأ في جلب رصيد المحفظة: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
        'balance': 0.0,
        'currency': 'EGP',
      };
    }
  }

  /// الحصول على تاريخ المعاملات للصيدلية
  static Future<Map<String, dynamic>> getPharmacyTransactions({
    int page = 1,
    int limit = 20,
    String? type, // 'credit', 'debit'
    String? startDate,
    String? endDate,
  }) async {
    try {
      print('📋 جلب تاريخ معاملات الصيدلية...');

      String url = '${AppConfig.baseUrl}/wallet/pharmacy/transactions?page=$page&limit=$limit';
      if (type != null) url += '&type=$type';
      if (startDate != null) url += '&startDate=$startDate';
      if (endDate != null) url += '&endDate=$endDate';

      final response = await http.get(
        Uri.parse(url),
        headers: await pharmacyAuthHeaders,
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        return {
          'success': true,
          'transactions': responseData['data']['transactions'] ?? [],
          'pagination': responseData['data']['pagination'],
          'summary': responseData['data']['summary'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في جلب المعاملات',
          'transactions': [],
        };
      }
    } catch (e) {
      print('❌ خطأ في جلب المعاملات: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
        'transactions': [],
      };
    }
  }

  /// طلب سحب من المحفظة
  static Future<Map<String, dynamic>> requestWithdrawal({
    required double amount,
    required String bankAccount,
    required String bankName,
    String? notes,
  }) async {
    try {
      print('💸 طلب سحب من المحفظة...');
      AppConfig.printNetworkInfo();

      final requestBody = {
        'amount': amount,
        'bankAccount': bankAccount,
        'bankName': bankName,
        'notes': notes,
        'currency': 'EGP',
      };

      print('📤 إرسال طلب السحب: ${jsonEncode(requestBody)}');

      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}/wallet/pharmacy/withdraw'),
        headers: await pharmacyAuthHeaders,
        body: jsonEncode(requestBody),
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');
      print('📄 محتوى الاستجابة: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 201 && responseData['success'] == true) {
        return {
          'success': true,
          'message': responseData['message'] ?? 'تم إرسال طلب السحب بنجاح',
          'data': responseData['data'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في إرسال طلب السحب',
          'errors': responseData['errors'],
        };
      }
    } catch (e) {
      print('❌ خطأ في طلب السحب: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
      };
    }
  }

  /// الحصول على طلبات السحب
  static Future<Map<String, dynamic>> getWithdrawalRequests({
    int page = 1,
    int limit = 10,
    String? status, // 'pending', 'approved', 'rejected'
  }) async {
    try {
      print('📋 جلب طلبات السحب...');

      String url = '${AppConfig.baseUrl}/wallet/pharmacy/withdrawals?page=$page&limit=$limit';
      if (status != null) url += '&status=$status';

      final response = await http.get(
        Uri.parse(url),
        headers: await pharmacyAuthHeaders,
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      print('📥 استجابة الخادم: ${response.statusCode}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        return {
          'success': true,
          'withdrawals': responseData['data']['withdrawals'] ?? [],
          'pagination': responseData['data']['pagination'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'فشل في جلب طلبات السحب',
          'withdrawals': [],
        };
      }
    } catch (e) {
      print('❌ خطأ في جلب طلبات السحب: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: ${e.toString()}',
        'withdrawals': [],
      };
    }
  }

  /// تنسيق المبلغ بالعملة المصرية
  static String formatCurrency(double amount, {String currency = 'EGP'}) {
    if (currency == 'EGP') {
      return '${amount.toStringAsFixed(2)} ج.م';
    }
    return '${amount.toStringAsFixed(2)} $currency';
  }

  /// تحويل المبلغ إلى نص
  static String formatAmount(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}م';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}ك';
    }
    return amount.toStringAsFixed(0);
  }

  /// الحصول على لون المعاملة حسب النوع
  static Color getTransactionColor(String type) {
    switch (type.toLowerCase()) {
      case 'credit':
      case 'deposit':
        return const Color(0xFF4CAF50); // أخضر للإيداع
      case 'debit':
      case 'withdrawal':
        return const Color(0xFFF44336); // أحمر للسحب
      default:
        return const Color(0xFF757575); // رمادي للأخرى
    }
  }

  /// الحصول على أيقونة المعاملة حسب النوع
  static IconData getTransactionIcon(String type) {
    switch (type.toLowerCase()) {
      case 'credit':
      case 'deposit':
        return Icons.add_circle_outline;
      case 'debit':
      case 'withdrawal':
        return Icons.remove_circle_outline;
      case 'order_payment':
        return Icons.shopping_cart_outlined;
      default:
        return Icons.account_balance_wallet_outlined;
    }
  }

  /// الحصول على وصف المعاملة
  static String getTransactionDescription(Map<String, dynamic> transaction) {
    final type = transaction['type']?.toString().toLowerCase() ?? '';
    final orderId = transaction['orderId']?.toString() ?? '';
    
    switch (type) {
      case 'credit':
        if (orderId.isNotEmpty) {
          return 'دفعة من طلب #$orderId';
        }
        return 'إيداع في المحفظة';
      case 'debit':
        return 'سحب من المحفظة';
      case 'order_payment':
        return 'دفعة طلب #$orderId';
      default:
        return transaction['description']?.toString() ?? 'معاملة';
    }
  }
}
