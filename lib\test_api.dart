import 'package:flutter/material.dart';
import 'services/api_service.dart';

class TestApiScreen extends StatefulWidget {
  const TestApiScreen({super.key});

  @override
  State<TestApiScreen> createState() => _TestApiScreenState();
}

class _TestApiScreenState extends State<TestApiScreen> {
  String _result = 'اضغط على الزر لاختبار API';
  bool _isLoading = false;

  Future<void> _testRegistration() async {
    setState(() {
      _isLoading = true;
      _result = 'جاري اختبار التسجيل...';
    });

    try {
      final apiService = ApiService();
      final result = await apiService.register(
        name: 'أحمد محمد',
        email: 'test${DateTime.now().millisecondsSinceEpoch}@example.com',
        phone: '01234567890',
        password: 'password123',
        address: 'شارع النيل، المعادي',
        governorate: 'القاهرة',
      );

      setState(() {
        _result = 'نجح التسجيل!\n${result['message']}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'فشل التسجيل:\n$e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار API'),
        backgroundColor: const Color(0xFF00BF63),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: _isLoading ? null : _testRegistration,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF00BF63),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                minimumSize: const Size(double.infinity, 50),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text(
                      'اختبار التسجيل',
                      style: TextStyle(fontSize: 16, fontFamily: 'Tajawal'),
                    ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _result,
                    style: const TextStyle(
                      fontSize: 14,
                      fontFamily: 'Tajawal',
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
