/// مساعد تنسيق العملة المصرية
class CurrencyFormatter {
  static const String _currency = 'EGP';
  static const String _symbol = 'ج.م';

  /// تنسيق المبلغ بالجنيه المصري
  static String format(double amount, {bool showSymbol = true}) {
    if (showSymbol) {
      return '${amount.toStringAsFixed(2)} $_symbol';
    }
    return amount.toStringAsFixed(2);
  }

  /// تنسيق المبلغ بدون الكسور العشرية إذا كانت صفر
  static String formatClean(double amount, {bool showSymbol = true}) {
    String formatted;
    if (amount == amount.roundToDouble()) {
      formatted = amount.round().toString();
    } else {
      formatted = amount.toStringAsFixed(2);
    }
    
    if (showSymbol) {
      return '$formatted $_symbol';
    }
    return formatted;
  }

  /// تنسيق المبلغ مع اختصار للأرقام الكبيرة
  static String formatCompact(double amount, {bool showSymbol = true}) {
    String formatted;
    
    if (amount >= 1000000) {
      formatted = '${(amount / 1000000).toStringAsFixed(1)}م';
    } else if (amount >= 1000) {
      formatted = '${(amount / 1000).toStringAsFixed(1)}ك';
    } else if (amount == amount.roundToDouble()) {
      formatted = amount.round().toString();
    } else {
      formatted = amount.toStringAsFixed(2);
    }
    
    if (showSymbol) {
      return '$formatted $_symbol';
    }
    return formatted;
  }

  /// تحويل النص إلى مبلغ
  static double? parseAmount(String text) {
    // إزالة الرموز والمسافات
    String cleanText = text
        .replaceAll(_symbol, '')
        .replaceAll('ج.م', '')
        .replaceAll('ج', '')
        .replaceAll(',', '')
        .trim();
    
    return double.tryParse(cleanText);
  }

  /// التحقق من صحة المبلغ
  static bool isValidAmount(String text) {
    final amount = parseAmount(text);
    return amount != null && amount >= 0;
  }

  /// الحصول على رمز العملة
  static String get currencySymbol => _symbol;

  /// الحصول على كود العملة
  static String get currencyCode => _currency;

  /// تنسيق المبلغ للعرض في الواجهة
  static String formatForDisplay(dynamic amount) {
    if (amount == null) return '0.00 $_symbol';
    
    double value;
    if (amount is String) {
      value = parseAmount(amount) ?? 0.0;
    } else if (amount is num) {
      value = amount.toDouble();
    } else {
      return '0.00 $_symbol';
    }
    
    return formatClean(value);
  }

  /// تنسيق المبلغ للإدخال في النماذج
  static String formatForInput(double amount) {
    if (amount == amount.roundToDouble()) {
      return amount.round().toString();
    }
    return amount.toStringAsFixed(2);
  }

  /// تنسيق المبلغ مع فاصلة الآلاف
  static String formatWithCommas(double amount, {bool showSymbol = true}) {
    String formatted = amount.toStringAsFixed(2);
    
    // إضافة فاصلة الآلاف
    List<String> parts = formatted.split('.');
    String integerPart = parts[0];
    String decimalPart = parts.length > 1 ? parts[1] : '00';
    
    // إضافة الفواصل للجزء الصحيح
    String formattedInteger = '';
    for (int i = 0; i < integerPart.length; i++) {
      if (i > 0 && (integerPart.length - i) % 3 == 0) {
        formattedInteger += ',';
      }
      formattedInteger += integerPart[i];
    }
    
    formatted = '$formattedInteger.$decimalPart';
    
    if (showSymbol) {
      return '$formatted $_symbol';
    }
    return formatted;
  }

  /// تنسيق النسبة المئوية
  static String formatPercentage(double percentage) {
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// تنسيق الخصم
  static String formatDiscount(double originalPrice, double discountedPrice) {
    final discount = originalPrice - discountedPrice;
    final percentage = (discount / originalPrice) * 100;
    
    return 'خصم ${formatClean(discount)} (${formatPercentage(percentage)})';
  }

  /// تنسيق المبلغ للمقارنة (السعر الأصلي والمخفض)
  static Map<String, String> formatPriceComparison(double originalPrice, double? discountedPrice) {
    if (discountedPrice == null || discountedPrice >= originalPrice) {
      return {
        'current': formatClean(originalPrice),
        'original': '',
        'discount': '',
      };
    }
    
    final discount = originalPrice - discountedPrice;
    final percentage = (discount / originalPrice) * 100;
    
    return {
      'current': formatClean(discountedPrice),
      'original': formatClean(originalPrice),
      'discount': formatPercentage(percentage),
    };
  }

  /// تنسيق تفاصيل الفاتورة
  static Map<String, String> formatInvoiceDetails({
    required double subtotal,
    double discount = 0,
    double tax = 0,
    double deliveryFee = 0,
  }) {
    final total = subtotal - discount + tax + deliveryFee;
    
    return {
      'subtotal': formatClean(subtotal),
      'discount': discount > 0 ? '- ${formatClean(discount)}' : '',
      'tax': tax > 0 ? formatClean(tax) : '',
      'deliveryFee': deliveryFee > 0 ? formatClean(deliveryFee) : '',
      'total': formatClean(total),
    };
  }

  /// تنسيق المبلغ للتصدير (CSV, Excel)
  static String formatForExport(double amount) {
    return amount.toStringAsFixed(2);
  }

  /// تنسيق المبلغ للطباعة
  static String formatForPrint(double amount) {
    return formatWithCommas(amount);
  }
}
