import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:http/http.dart' as http;

class NetworkDiscovery {
  static const String _port = '3000';
  static const Duration _timeout = Duration(seconds: 3);
  
  /// اكتشاف IP الخادم تلقائياً
  static Future<String?> discoverServerIP() async {
    try {
      // إذا كان التطبيق يعمل على الويب، استخدم localhost
      if (kIsWeb) {
        print('🌐 التطبيق يعمل على الويب، سيتم استخدام localhost');
        final isLocalhost = await _testServerIP('localhost');
        if (isLocalhost) {
          return 'localhost';
        }
        return null;
      }

      // الحصول على IP الجهاز الحالي
      final deviceIP = await _getDeviceIP();
      if (deviceIP == null) {
        print('❌ لا يمكن الحصول على IP الجهاز');
        return null;
      }

      print('📱 IP الجهاز: $deviceIP');

      // استخراج شبكة الجهاز (مثل 192.168.1.x)
      final networkBase = _getNetworkBase(deviceIP);
      if (networkBase == null) {
        print('❌ لا يمكن تحديد شبكة الجهاز');
        return null;
      }

      print('🌐 شبكة الجهاز: $networkBase.x');

      // البحث عن الخادم في الشبكة
      final serverIP = await _scanForServer(networkBase);

      if (serverIP != null) {
        print('✅ تم العثور على الخادم في: $serverIP');
      } else {
        print('❌ لم يتم العثور على الخادم في الشبكة');
      }

      return serverIP;

    } catch (e) {
      print('❌ خطأ في اكتشاف الخادم: $e');
      return null;
    }
  }
  
  /// الحصول على IP الجهاز الحالي
  static Future<String?> _getDeviceIP() async {
    try {
      final info = NetworkInfo();
      
      // محاولة الحصول على IP من WiFi
      final wifiIP = await info.getWifiIP();
      if (wifiIP != null && wifiIP.isNotEmpty && wifiIP != '0.0.0.0') {
        return wifiIP;
      }
      
      // إذا فشل WiFi، استخدم طريقة أخرى
      return await _getIPFromSocket();
      
    } catch (e) {
      print('خطأ في الحصول على IP: $e');
      return await _getIPFromSocket();
    }
  }
  
  /// الحصول على IP باستخدام Socket
  static Future<String?> _getIPFromSocket() async {
    try {
      final socket = await Socket.connect('*******', 80);
      final ip = socket.address.address;
      socket.destroy();
      return ip;
    } catch (e) {
      print('خطأ في Socket: $e');
      return null;
    }
  }
  
  /// استخراج قاعدة الشبكة من IP
  static String? _getNetworkBase(String ip) {
    final parts = ip.split('.');
    if (parts.length != 4) return null;
    
    // إرجاع أول 3 أجزاء (مثل 192.168.1)
    return '${parts[0]}.${parts[1]}.${parts[2]}';
  }
  
  /// البحث عن الخادم في الشبكة
  static Future<String?> _scanForServer(String networkBase) async {
    print('🔍 البحث عن الخادم في الشبكة...');
    
    // قائمة بالعناوين المحتملة للخادم
    final commonIPs = [
      '$networkBase.1',   // Router عادة
      '$networkBase.100', // عنوان شائع
      '$networkBase.101',
      '$networkBase.10',
      '$networkBase.11',
      '$networkBase.12',
      '$networkBase.13',
      '$networkBase.14',
      '$networkBase.15',
      '$networkBase.16',
      '$networkBase.17',
      '$networkBase.18',
      '$networkBase.19',
      '$networkBase.20',
    ];
    
    // اختبار كل IP بشكل متوازي
    final futures = commonIPs.map((ip) => _testServerIP(ip));
    final results = await Future.wait(futures);
    
    // العثور على أول IP يعمل
    for (int i = 0; i < results.length; i++) {
      if (results[i]) {
        return commonIPs[i];
      }
    }
    
    return null;
  }
  
  /// اختبار IP محدد للخادم
  static Future<bool> _testServerIP(String ip) async {
    try {
      final url = 'http://$ip:$_port/health';
      final response = await http.get(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
      ).timeout(_timeout);
      
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
  
  /// اختبار اتصال مع IP محدد
  static Future<Map<String, dynamic>> testConnection(String ip) async {
    final result = <String, dynamic>{
      'success': false,
      'message': '',
      'ip': ip,
      'url': 'http://$ip:$_port',
    };
    
    try {
      final url = 'http://$ip:$_port/health';
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        result['success'] = true;
        result['message'] = 'الاتصال ناجح مع $ip ✅';
        result['response'] = response.body;
      } else {
        result['message'] = 'الخادم يرد من $ip ولكن برمز خطأ: ${response.statusCode}';
      }
      
    } catch (e) {
      result['message'] = 'فشل الاتصال مع $ip: ${e.toString()}';
    }
    
    return result;
  }
}
