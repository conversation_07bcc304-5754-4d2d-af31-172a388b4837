import 'package:flutter/material.dart';

class Flutter3DView extends StatefulWidget {
  const Flutter3DView({super.key});

  @override
  State<Flutter3DView> createState() => _Flutter3DViewState();
}

class _Flutter3DViewState extends State<Flutter3DView>
    with TickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    )..repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF667eea), Color(0xFF764ba2), Color(0xFFf093fb)],
        ),
      ),
      child: Stack(
        children: [
          // Animated particles
          ...List.generate(20, (index) => _buildParticle(index)),
        ],
      ),
    );
  }

  Widget _buildParticle(int index) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Positioned(
          left:
              (index * 50.0 + _animationController.value * 100) %
              MediaQuery.of(context).size.width,
          top:
              (index * 30.0 + _animationController.value * 50) %
              MediaQuery.of(context).size.height,
          child: Container(
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.3),
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }
}
