import 'package:flutter/material.dart';

class ThreeDCard extends StatefulWidget {
  final Widget child;
  final double elevation;

  const ThreeDCard({super.key, required this.child, this.elevation = 4.0});

  @override
  State<ThreeDCard> createState() => _ThreeDCardState();
}

class _ThreeDCardState extends State<ThreeDCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(begin: 0.0, end: 0.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform(
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.001)
            ..rotateX(_rotationAnimation.value)
            ..rotateY(_rotationAnimation.value),
          alignment: Alignment.center,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: widget.elevation * 2,
                    spreadRadius: widget.elevation / 2,
                    offset: Offset(0, widget.elevation),
                  ),
                ],
              ),
              child: widget.child,
            ),
          ),
        );
      },
    );
  }

  void onHover(bool isHovered) {
    if (isHovered) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }
}
