import 'dart:convert';
import 'package:http/http.dart' as http;
import 'lib/utils/network_discovery.dart';
import 'lib/config/app_config.dart';

void main() async {
  print('🔍 بدء اختبار النظام الجديد...');

  // اكتشاف الخادم تلقائياً
  await AppConfig.discoverServer();

  // معلومات الشبكة
  final String baseUrl = 'http://${AppConfig.serverIP}:3000';
  final String healthUrl = '$baseUrl/health';
  
  print('=== معلومات الشبكة ===');
  print('Base URL: $baseUrl');
  print('Health URL: $healthUrl');
  print('====================');
  
  try {
    print('📡 اختبار الاتصال مع: $healthUrl');
    
    final response = await http.get(
      Uri.parse(healthUrl),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ).timeout(
      const Duration(seconds: 10),
      onTimeout: () {
        throw Exception('انتهت مهلة الاتصال');
      },
    );
    
    print('📊 Status Code: ${response.statusCode}');
    print('📋 Response Headers: ${response.headers}');
    print('📄 Response Body: ${response.body}');
    
    if (response.statusCode == 200) {
      print('✅ الاتصال ناجح!');
      
      // محاولة تحليل JSON
      try {
        final jsonData = json.decode(response.body);
        print('📊 JSON Data: $jsonData');
      } catch (e) {
        print('⚠️ فشل في تحليل JSON: $e');
      }
    } else {
      print('❌ الخادم يرد ولكن برمز خطأ: ${response.statusCode}');
    }
    
  } catch (e) {
    print('❌ فشل الاتصال: $e');
    
    // تحليل نوع الخطأ
    if (e.toString().contains('SocketException')) {
      print('\n💡 نصائح لحل المشكلة:');
      print('• تأكد من أن الهاتف والكمبيوتر على نفس الشبكة');
      print('• تأكد من أن الخادم يعمل على $baseUrl');
      print('• تحقق من إعدادات الجدار الناري (Firewall)');
    } else if (e.toString().contains('TimeoutException')) {
      print('\n💡 المشكلة: انتهت مهلة الاتصال');
      print('• تحقق من سرعة الإنترنت');
      print('• تأكد من أن الخادم يعمل');
    }
  }
  
  print('\n🏁 انتهى الاختبار');
}
