const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// بيانات المستخدم والصيدلية للاختبار
const testUser = {
  email: '<EMAIL>',
  password: 'password123',
  name: 'مستخدم تجريبي',
  phone: '+201234567890',
  governorate: 'القاهرة'
};

const testPharmacy = {
  email: '<EMAIL>',
  password: 'password123',
  name: 'صيدلية تجريبية',
  phone: '+201234567891',
  licenseNumber: 'LIC123456',
  address: {
    street: 'شارع التحرير',
    city: 'القاهرة',
    governorate: 'القاهرة'
  }
};

let userToken = '';
let pharmacyToken = '';
let orderId = '';
let offerId = '';

async function registerUser() {
  try {
    console.log('🔐 تسجيل مستخدم جديد...');
    const response = await axios.post(`${BASE_URL}/auth/register`, testUser);
    console.log('✅ تم تسجيل المستخدم بنجاح');
    return response.data.token;
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.message?.includes('موجود')) {
      console.log('📝 المستخدم موجود مسبقاً، سيتم تسجيل الدخول...');
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
        email: testUser.email,
        password: testUser.password
      });
      return loginResponse.data.token;
    }
    throw error;
  }
}

async function registerPharmacy() {
  try {
    console.log('🏥 تسجيل صيدلية جديدة...');
    const response = await axios.post(`${BASE_URL}/auth/pharmacy/register`, testPharmacy);
    console.log('✅ تم تسجيل الصيدلية بنجاح');
    return response.data.token;
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.message?.includes('موجود')) {
      console.log('📝 الصيدلية موجودة مسبقاً، سيتم تسجيل الدخول...');
      const loginResponse = await axios.post(`${BASE_URL}/auth/pharmacy/login`, {
        email: testPharmacy.email,
        password: testPharmacy.password
      });
      return loginResponse.data.token;
    }
    throw error;
  }
}

async function createOrder() {
  try {
    console.log('📝 إنشاء طلب جديد...');
    const response = await axios.post(`${BASE_URL}/orders`, {
      medicineName: 'باراسيتامول 500 مجم',
      quantity: 2,
      description: 'دواء للصداع',
      userGovernorate: 'القاهرة'
    }, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    
    orderId = response.data.data._id;
    console.log(`✅ تم إنشاء الطلب بنجاح: ${orderId}`);
    return orderId;
  } catch (error) {
    console.error('❌ خطأ في إنشاء الطلب:', error.response?.data || error.message);
    throw error;
  }
}

async function submitOffer() {
  try {
    console.log('💰 تقديم عرض على الطلب...');
    const response = await axios.post(`${BASE_URL}/orders/${orderId}/offer`, {
      price: 25.50,
      availableQuantity: 2,
      notes: 'متوفر حالياً',
      estimatedTime: 30
    }, {
      headers: { Authorization: `Bearer ${pharmacyToken}` }
    });
    
    console.log('✅ تم تقديم العرض بنجاح');
    return response.data;
  } catch (error) {
    console.error('❌ خطأ في تقديم العرض:', error.response?.data || error.message);
    throw error;
  }
}

async function getOrderOffers() {
  try {
    console.log('📋 جلب عروض الطلب...');
    const response = await axios.get(`${BASE_URL}/orders/${orderId}/offers`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    
    const offers = response.data.data.offers;
    if (offers && offers.length > 0) {
      offerId = offers[0]._id;
      console.log(`✅ تم العثور على ${offers.length} عرض، معرف العرض الأول: ${offerId}`);
      return offers;
    } else {
      throw new Error('لم يتم العثور على عروض');
    }
  } catch (error) {
    console.error('❌ خطأ في جلب العروض:', error.response?.data || error.message);
    throw error;
  }
}

async function acceptOffer() {
  try {
    console.log('✅ قبول العرض...');
    const response = await axios.put(`${BASE_URL}/orders/${orderId}/offers/${offerId}/accept`, {}, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    
    console.log('✅ تم قبول العرض بنجاح!');
    console.log('📊 تفاصيل الطلب المقبول:', response.data.data);
    return response.data;
  } catch (error) {
    console.error('❌ خطأ في قبول العرض:', error.response?.data || error.message);
    throw error;
  }
}

async function checkPharmacyOrders() {
  try {
    console.log('🏥 فحص الطلبات المقبولة للصيدلية...');
    const response = await axios.get(`${BASE_URL}/orders/pharmacy?status=confirmed`, {
      headers: { Authorization: `Bearer ${pharmacyToken}` }
    });
    
    const orders = response.data.data;
    console.log(`📋 عدد الطلبات المقبولة: ${orders.length}`);
    
    if (orders.length > 0) {
      console.log('✅ الطلبات المقبولة:');
      orders.forEach((order, index) => {
        console.log(`${index + 1}. طلب ${order.orderNumber}: ${order.medicineRequest?.name} - الحالة: ${order.status}`);
      });
    } else {
      console.log('❌ لا توجد طلبات مقبولة');
    }
    
    return orders;
  } catch (error) {
    console.error('❌ خطأ في جلب طلبات الصيدلية:', error.response?.data || error.message);
    throw error;
  }
}

async function runTest() {
  try {
    console.log('🚀 بدء اختبار تدفق الطلبات...\n');
    
    // 1. تسجيل المستخدم والصيدلية
    userToken = await registerUser();
    pharmacyToken = await registerPharmacy();
    
    console.log('\n');
    
    // 2. إنشاء طلب
    await createOrder();
    
    console.log('\n');
    
    // 3. تقديم عرض
    await submitOffer();
    
    console.log('\n');
    
    // 4. جلب العروض
    await getOrderOffers();
    
    console.log('\n');
    
    // 5. قبول العرض
    await acceptOffer();
    
    console.log('\n');
    
    // 6. فحص الطلبات المقبولة للصيدلية
    await checkPharmacyOrders();
    
    console.log('\n✅ تم اكتمال الاختبار بنجاح!');
    console.log('🎉 الآن يمكن للصيدلية رؤية الطلب المقبول في قسم "الطلبات المقبولة"');
    
  } catch (error) {
    console.error('\n❌ فشل الاختبار:', error.message);
  }
}

// تشغيل الاختبار
runTest();
